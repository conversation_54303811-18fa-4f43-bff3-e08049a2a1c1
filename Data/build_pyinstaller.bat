@echo off
chcp 65001 >nul

echo 正在打包 皇冠比赛数据显示工具...
echo.

REM 清理之前的打包文件
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "*.spec" del /q *.spec

REM PyInstaller打包命令 - 增强兼容性版本
pyinstaller ^
    --onefile ^
    --windowed ^
    --name=皇冠比赛数据显示工具 ^
    --icon=icon.ico ^
    --add-data="Config.ini;." ^
    --add-data="requirements.txt;." ^
    --add-data="README.md;." ^
    --hidden-import=PyQt6 ^
    --hidden-import=PyQt6.QtCore ^
    --hidden-import=PyQt6.QtGui ^
    --hidden-import=PyQt6.QtWidgets ^
    --hidden-import=PyQt6.sip ^
    --hidden-import=sip ^
    --hidden-import=requests ^
    --hidden-import=urllib3 ^
    --hidden-import=certifi ^
    --hidden-import=hashlib ^
    --hidden-import=uuid ^
    --hidden-import=platform ^
    --hidden-import=json ^
    --hidden-import=time ^
    --hidden-import=logging ^
    --hidden-import=traceback ^
    --hidden-import=configparser ^
    --hidden-import=subprocess ^
    --hidden-import=smtplib ^
    --hidden-import=email.mime.text ^
    --hidden-import=email.mime.multipart ^
    --hidden-import=threading ^
    --hidden-import=queue ^
    --hidden-import=datetime ^
    --hidden-import=re ^
    --hidden-import=os ^
    --hidden-import=sys ^
    --hidden-import=xml.etree.ElementTree ^
    --hidden-import=xml.etree ^
    --hidden-import=xml ^
    --collect-all=PyQt6 ^
    --collect-all=requests ^
    --collect-all=urllib3 ^
    --collect-all=certifi ^
    --collect-submodules=PyQt6 ^
    --copy-metadata=PyQt6 ^
    --copy-metadata=requests ^
    --copy-metadata=urllib3 ^
    --copy-metadata=certifi ^
    --noconfirm ^
    Data.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 打包成功！
    echo 📁 输出目录: dist\
    echo 🚀 可执行文件: dist\皇冠比赛数据显示工具.exe
    echo.
    echo 📋 打包信息:
    echo    - 主程序: Data.py
    echo    - 配置文件: Config.ini
    echo    - 依赖文件: requirements.txt
    echo    - 说明文档: README.md
    echo.
    echo 💡 提示: 打包后的程序会自动创建配置文件
) else (
    echo.
    echo ❌ 打包失败！错误代码: %ERRORLEVEL%
    echo.
    echo 🔧 可能的解决方案:
    echo    1. 确保已安装 PyInstaller: pip install pyinstaller
    echo    2. 确保已安装所有依赖: pip install -r requirements.txt
    echo    3. 检查 Python 环境是否正确
    echo    4. 检查是否有文件被占用
)

pause 