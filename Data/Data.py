import sys
import json
import configparser
import os
import requests
import time
import xml.etree.ElementTree as ET
from urllib3 import disable_warnings
from urllib3.exceptions import InsecureRequestWarning
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QLabel, QLineEdit, 
                             QPushButton, QTabWidget, QTableWidget, QTableWidgetItem,
                             QTextEdit, QGroupBox, QScrollArea, QFrame, QSplitter,
                             QListWidget, QListWidgetItem, QStatusBar, QMessageBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QPalette, QColor

# 禁用SSL警告
disable_warnings(InsecureRequestWarning)


class LoginThread(QThread):
    """登录线程"""
    login_result = pyqtSignal(bool, dict, str)  # 是否成功, 用户信息, 错误信息

    def __init__(self, domain, username, password, ver, mobile_mode=False):
        super().__init__()
        self.domain = domain
        self.username = username
        self.password = password
        self.ver = ver
        self.mobile_mode = mobile_mode

    def run(self):
        try:
            domain = self.domain
            username = self.username
            password = self.password
            ver = self.ver
            
            if not domain.startswith(("http://", "https://")):
                domain = f"https://{domain}"
            login_url = f"{domain}/transform.php"
            
            # 根据手机版模式设置不同的参数
            if self.mobile_mode:
                app_param = "Y"  # 手机版使用app=Y
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                app_param = "N"  # 桌面版使用app=N
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            
            payload = (
                f"p=chk_login"
                f"&langx=zh-cn"
                f"&ver={ver}"
                f"&username={username}"
                f"&password={password}"
                f"&app={app_param}&auto=IHGCFD&blackbox="
                f"&userAgent=TW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMC4wLjAuMCBTYWZhcmkvNTM3LjM2"
            )
            headers = {
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            
            response = make_request(login_url, payload, headers, timeout=15, description="用户登录")
            root = ET.fromstring(response.text)
            status = root.find("status")
            
            if status is not None and status.text == "200":
                uid_element = root.find("uid")
                if uid_element is not None:
                    user_info = {"uid": uid_element.text, "ver": ver}
                    self.login_result.emit(True, user_info, "")
                    return
                else:
                    self.login_result.emit(False, {}, "登录响应中未找到UID")
                    return
            else:
                status_text = status.text if status is not None else "未知"
                error_msg = get_error_message(status_text)
                self.login_result.emit(False, {}, f"登录失败 {error_msg}")
                return
                
        except Exception as e:
            self.login_result.emit(False, {}, f"登录异常: {str(e)}")


class GetMatchesThread(QThread):
    """获取比赛列表线程"""
    matches_result = pyqtSignal(bool, list, str, int)  # 是否成功, 比赛列表, 错误信息, 总比赛数

    def __init__(self, domain, user_info, mobile_mode=False):
        super().__init__()
        self.domain = domain
        self.user_info = user_info
        self.mobile_mode = mobile_mode

    def run(self):
        try:
            domain = self.domain
            uid = self.user_info.get("uid")
            ver = self.user_info.get("ver")
            
            if not uid or not ver:
                self.matches_result.emit(False, [], "用户信息不完整，请重新登录", 0)
                return
            
            if not domain.startswith(("http://", "https://")):
                domain = f"https://{domain}"
            matches_url = f"{domain}/transform.php"
            
            # 生成时间戳
            ts = int(time.time() * 1000)
            
            # 根据手机版模式设置不同的参数
            if self.mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            
            payload = (
                f"uid={uid}"
                f"&ver={ver}"
                f"&langx=zh-cn"
                f"&p=get_game_list"
                f"&p3type="
                f"&date="
                f"&gtype=ft"
                f"&showtype=live"
                f"&rtype=rb"
                f"&ltype=3"
                f"&filter="
                f"&cupFantasy=N"
                f"&sorttype=L"
                f"&specialClick="
                f"&isFantasy=N"
                f"&ts={ts}"
            )
            headers = {
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            
            response = make_request(matches_url, payload, headers, timeout=15, description="获取比赛列表")
            root = ET.fromstring(response.text)
            
            # 获取dataCount字段
            data_count = 0
            data_count_elem = root.find("dataCount")
            if data_count_elem is not None:
                try:
                    data_count = int(data_count_elem.text)
                except (ValueError, TypeError):
                    data_count = 0
            
            # 解析比赛数据
            matches = []
            for ec in root.findall(".//ec"):
                for game in ec.findall(".//game"):
                    match_data = {
                        "gid": game.find("GID").text if game.find("GID") is not None else "",
                        "league": game.find("LEAGUE").text if game.find("LEAGUE") is not None else "",
                        "team_h": game.find("TEAM_H").text if game.find("TEAM_H") is not None else "",
                        "team_c": game.find("TEAM_C").text if game.find("TEAM_C") is not None else "",
                        "score_h": game.find("SCORE_H").text if game.find("SCORE_H") is not None else "",
                        "score_c": game.find("SCORE_C").text if game.find("SCORE_C") is not None else "",
                        "datetime": game.find("DATETIME").text if game.find("DATETIME") is not None else "",
                        "retimeset": game.find("RETIMESET").text if game.find("RETIMESET") is not None else "",
                        "now_model": game.find("NOW_MODEL").text if game.find("NOW_MODEL") is not None else "",
                        "strong": game.find("STRONG").text if game.find("STRONG") is not None else "",
                        "ecid": game.find("ECID").text if game.find("ECID") is not None else "",
                        "lid": game.find("LID").text if game.find("LID") is not None else "",
                        "game_id": game.get("id", ""),
                        # 盘口数据
                        "ratio_re": game.find("RATIO_RE").text if game.find("RATIO_RE") is not None else "",
                        "ior_reh": game.find("IOR_REH").text if game.find("IOR_REH") is not None else "",
                        "ior_rec": game.find("IOR_REC").text if game.find("IOR_REC") is not None else "",
                        "ratio_rouo": game.find("RATIO_ROUO").text if game.find("RATIO_ROUO") is not None else "",
                        "ior_rouh": game.find("IOR_ROUH").text if game.find("IOR_ROUH") is not None else "",
                        "ior_rouc": game.find("IOR_ROUC").text if game.find("IOR_ROUC") is not None else "",
                        "ior_reoo": game.find("IOR_REOO").text if game.find("IOR_REOO") is not None else "",
                        "ior_reoe": game.find("IOR_REOE").text if game.find("IOR_REOE") is not None else "",
                        "ior_rtsy": game.find("IOR_RTSY").text if game.find("IOR_RTSY") is not None else "",
                        "ior_rtsn": game.find("IOR_RTSN").text if game.find("IOR_RTSN") is not None else "",
                        "ratio_rouho": game.find("RATIO_ROUHO").text if game.find("RATIO_ROUHO") is not None else "",
                        "ior_rouho": game.find("IOR_ROUHO").text if game.find("IOR_ROUHO") is not None else "",
                        "ior_rouhu": game.find("IOR_ROUHU").text if game.find("IOR_ROUHU") is not None else "",
                        "ratio_rouco": game.find("RATIO_ROUCO").text if game.find("RATIO_ROUCO") is not None else "",
                        "ior_rouco": game.find("IOR_ROUCO").text if game.find("IOR_ROUCO") is not None else "",
                        "ior_roucu": game.find("IOR_ROUCU").text if game.find("IOR_ROUCU") is not None else ""
                    }
                    matches.append(match_data)
            
            self.matches_result.emit(True, matches, "", data_count)
            
        except Exception as e:
            self.matches_result.emit(False, [], f"获取比赛列表异常: {str(e)}", 0)

class GetDetailedOddsThread(QThread):
    """获取详细盘口信息线程"""
    detailed_odds_result = pyqtSignal(bool, dict, str)  # 是否成功, 详细盘口数据, 错误信息
    
    def __init__(self, domain, user_info, lid, ecid, mobile_mode=False):
        super().__init__()
        self.domain = domain
        self.user_info = user_info
        self.lid = lid
        self.ecid = ecid
        self.mobile_mode = mobile_mode
    
    def run(self):
        try:
            domain = self.domain
            uid = self.user_info.get("uid")
            ver = self.user_info.get("ver")
            
            if not uid or not ver:
                self.detailed_odds_result.emit(False, {}, "用户信息不完整，请重新登录")
                return
            
            if not domain.startswith(("http://", "https://")):
                domain = f"https://{domain}"
            odds_url = f"{domain}/transform.php"
            
            # 生成时间戳
            ts = int(time.time() * 1000)
            
            # 根据手机版模式设置不同的参数
            if self.mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            
            payload = (
                f"uid={uid}"
                f"&ver={ver}"
                f"&langx=zh-cn"
                f"&p=get_game_more"
                f"&gtype=ft"
                f"&showtype=live"
                f"&ltype=3"
                f"&isRB=Y"
                f"&lid={self.lid}"
                f"&specialClick="
                f"&mode=CUP"
                f"&filter=Main"
                f"&ts={ts}"
                f"&ecid={self.ecid}"
            )
            headers = {
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            
            response = make_request(odds_url, payload, headers, timeout=15, description="获取详细盘口信息")
            root = ET.fromstring(response.text)
            
            # 解析详细盘口数据
            detailed_odds = {}
            
            # 只处理第一个<game>标签
            first_game = root.find("game")
            if first_game is not None:
                # 智能解析总进球数盘口
                sw_rt = first_game.find("sw_RT")
                if sw_rt is not None and sw_rt.text == "Y":
                    total_goals_odds = {}
                    
                    # 从sw_RT开始向下遍历，直到遇到下一个sw_开头的标签
                    # 获取所有子元素，找到sw_RT的位置，然后遍历后续元素
                    all_elements = list(first_game)
                    sw_rt_index = -1
                    
                    # 找到sw_RT的位置
                    for i, elem in enumerate(all_elements):
                        if elem.tag == "sw_RT":
                            sw_rt_index = i
                            break
                    
                    if sw_rt_index >= 0:
                        # 从sw_RT的下一个元素开始遍历
                        for i in range(sw_rt_index + 1, len(all_elements)):
                            elem = all_elements[i]
                            tag = elem.tag
                            
                            # 如果遇到下一个sw_开头的标签，停止遍历
                            if tag.startswith("sw_"):
                                break
                            
                            # 处理ior_RT开头的标签和ior_ROVER
                            if (tag.startswith("ior_RT") or tag == "ior_ROVER") and elem.text:
                                # 过滤掉赔率为0的盘口
                                try:
                                    odds_value = float(elem.text)
                                    if odds_value <= 0:
                                        continue
                                except ValueError:
                                    continue
                                
                                # 根据标签名智能推断进球范围
                                if tag == "ior_RT01":
                                    description = "0-1球"
                                elif tag == "ior_RT23":
                                    description = "2-3球"
                                elif tag == "ior_RT46":
                                    description = "4-6球"
                                elif tag == "ior_RT78":
                                    description = "7-8球"
                                elif tag == "ior_RT9O":
                                    description = "9球以上"
                                elif tag == "ior_ROVER":
                                    description = "7球以上"
                                else:
                                    # 尝试从标签名解析数字范围
                                    import re
                                    numbers = re.findall(r'\d+', tag)
                                    if len(numbers) >= 2:
                                        description = f"{numbers[0]}-{numbers[1]}球"
                                    elif len(numbers) == 1:
                                        description = f"{numbers[0]}球以上"
                                    else:
                                        description = tag  # 如果无法解析，使用原始标签名
                                
                                total_goals_odds[description] = elem.text
                    
                    if total_goals_odds:
                        detailed_odds["总进球数"] = total_goals_odds
                        print(f"解析到总进球数盘口: {total_goals_odds}")
                    else:
                        print("未解析到总进球数盘口数据")
            
            self.detailed_odds_result.emit(True, detailed_odds, "")
            
        except Exception as e:
            self.detailed_odds_result.emit(False, {}, f"获取详细盘口信息异常: {str(e)}")

# 错误代码映射表
ERROR_CODE_MAP = {
    "0X001": "由于网站流量较高，请重新再试。谢谢",
    "0X002": "由于网站流量较高，请重新再试。谢谢",
    "0X003": "系统正在忙碌中，请稍后再试。",
    "0X004": "系统正在忙碌中，请稍后再试。",
    "0X005": "系统正在忙碌中，请稍后再试。",
    "0X006": "系统最佳化中，请稍后。",
    "0X007": "系统正在忙碌中，请稍后再试。",
    "0X008": "系统正在忙碌中，请稍后再试。",
    "1X014": "登入失败，请重新尝试。",
    "errorLogin": "现在我们的系统面临技术问题。请稍后再尝试登入。对于这样的不便我们深表抱歉，我们也正在全力的解决该问题。谢谢您的耐心等待。",
    "user_stop": "您的帐户已被停用，请联络您的上线开启你的帐号。",
    "user_forbid": "您的帐号已被禁止登入，请联络您的上线开启你的帐号！",
}

def get_error_message(code):
    """获取错误代码对应的中文描述"""
    if not code:
        return ""
    
    # 移除可能的空白字符
    code = str(code).strip()
    
    # 查找错误代码对应的描述
    error_msg = ERROR_CODE_MAP.get(code, "")
    
    if error_msg:
        return f"[{code}] {error_msg}"
    else:
        return f"[{code}] 未知错误代码"

def make_request(url, data=None, headers=None, method='POST', timeout=15, description=""):
    """统一的请求函数"""
    try:
        # 发送请求
        if method.upper() == 'GET':
            response = requests.get(url, params=data, headers=headers, timeout=timeout, verify=False)
        else:
            response = requests.post(url, data=data, headers=headers, timeout=timeout, verify=False)
        
        # 检查HTTP状态
        response.raise_for_status()
        
        return response
        
    except requests.exceptions.Timeout:
        raise Exception(f"请求超时 - {description}")
    except requests.exceptions.ConnectionError as e:
        raise Exception(f"连接错误 - {description}: {str(e)}")
    except requests.exceptions.HTTPError as e:
        raise Exception(f"HTTP错误 - {description}: {str(e)}")
    except Exception as e:
        raise Exception(f"请求异常 - {description}: {str(e)}")


class BettingOddsWidget(QWidget):
    """盘口信息显示组件"""
    
    odds_clicked = pyqtSignal(str, str)  # 信号：盘口类型, 盘口值
    
    def __init__(self, title, odds_data):
        super().__init__()
        self.title = title
        self.odds_data = odds_data
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 标题 - 根据盘口类型显示不同的标题
        if self.title in ["全场让球", "全场大小", "队伍1进球大小", "队伍2进球大小"]:
            # 对于有盘口值的类型，需要在标题中显示盘口值
            if "盘口" in self.odds_data:
                title_text = f"{self.title} ({self.odds_data['盘口']})"
            else:
                title_text = self.title
        else:
            title_text = self.title
        
        title_label = QLabel(title_text)
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #333; margin-bottom: 5px;")
        layout.addWidget(title_label)
        
        # 盘口选项网格 - 固定为2列布局
        grid_layout = QGridLayout()
        grid_layout.setHorizontalSpacing(10)
        grid_layout.setVerticalSpacing(5)
        
        # 获取盘口选项列表
        options = list(self.odds_data.items())
        
        # 如果是让球或大小类型，需要特殊处理
        if self.title in ["全场让球", "全场大小", "队伍1进球大小", "队伍2进球大小"]:
            # 过滤掉"盘口"字段，只显示选项按钮
            filtered_options = [(k, v) for k, v in options if k != "盘口"]
            
            if len(filtered_options) == 2:
                # 创建左右两个按钮
                left_option, left_odds = filtered_options[0]
                right_option, right_odds = filtered_options[1]
                
                # 检查赔率是否为0，如果是则隐藏按钮
                left_odds_value = 0
                right_odds_value = 0
                try:
                    left_odds_value = float(left_odds)
                    right_odds_value = float(right_odds)
                except ValueError:
                    pass
                
                # 左按钮
                left_button = QPushButton(f"{left_option}\n{left_odds}")
                left_button.setMinimumSize(80, 50)
                if left_odds_value <= 0:
                    left_button.setVisible(False)  # 隐藏封盘的按钮
                else:
                    left_button.setStyleSheet("""
                        QPushButton {
                            background-color: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 4px;
                            padding: 5px;
                            font-size: 9px;
                            color: #333;
                        }
                        QPushButton:hover {
                            background-color: #e9ecef;
                            border-color: #adb5bd;
                        }
                        QPushButton:pressed {
                            background-color: #007bff;
                            color: white;
                        }
                    """)
                    left_button.clicked.connect(lambda: self.odds_clicked.emit(self.title, f"{left_option}: {left_odds}"))
                
                # 右按钮
                right_button = QPushButton(f"{right_option}\n{right_odds}")
                right_button.setMinimumSize(80, 50)
                if right_odds_value <= 0:
                    right_button.setVisible(False)  # 隐藏封盘的按钮
                else:
                    right_button.setStyleSheet("""
                        QPushButton {
                            background-color: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 4px;
                            padding: 5px;
                            font-size: 9px;
                            color: #333;
                        }
                        QPushButton:hover {
                            background-color: #e9ecef;
                            border-color: #adb5bd;
                        }
                        QPushButton:pressed {
                            background-color: #007bff;
                            color: white;
                        }
                    """)
                    right_button.clicked.connect(lambda: self.odds_clicked.emit(self.title, f"{right_option}: {right_odds}"))
                
                # 添加到网格布局 - 左右分布
                grid_layout.addWidget(left_button, 0, 0)
                grid_layout.addWidget(right_button, 0, 1)
            else:
                # 普通处理
                for i, (option, odds) in enumerate(options):
                    odds_button = QPushButton(f"{option}\n{odds}")
                    odds_button.setMinimumSize(80, 50)
                    
                    # 检查赔率是否为0，如果是则隐藏按钮
                    odds_value = 0
                    try:
                        odds_value = float(odds)
                    except ValueError:
                        pass
                    
                    if odds_value <= 0:
                        odds_button.setVisible(False)  # 隐藏封盘的按钮
                    else:
                        odds_button.setStyleSheet("""
                            QPushButton {
                                background-color: #f8f9fa;
                                border: 1px solid #dee2e6;
                                border-radius: 4px;
                                padding: 5px;
                                font-size: 9px;
                                color: #333;
                            }
                            QPushButton:hover {
                                background-color: #e9ecef;
                                border-color: #adb5bd;
                            }
                            QPushButton:pressed {
                                background-color: #007bff;
                                color: white;
                            }
                        """)
                        odds_button.clicked.connect(lambda checked, opt=option, odd=odds: 
                                                 self.odds_clicked.emit(self.title, f"{opt}: {odd}"))
                    
                    grid_layout.addWidget(odds_button, i // 2, i % 2)
        else:
            # 普通处理（单双、双方球队进球等）
            for i, (option, odds) in enumerate(options):
                odds_button = QPushButton(f"{option}\n{odds}")
                odds_button.setMinimumSize(80, 50)
                
                # 检查赔率是否为0，如果是则隐藏按钮
                odds_value = 0
                try:
                    odds_value = float(odds)
                except ValueError:
                    pass
                
                if odds_value <= 0:
                    odds_button.setVisible(False)  # 隐藏封盘的按钮
                else:
                    odds_button.setStyleSheet("""
                        QPushButton {
                            background-color: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 4px;
                            padding: 5px;
                            font-size: 9px;
                            color: #333;
                        }
                        QPushButton:hover {
                            background-color: #e9ecef;
                            border-color: #adb5bd;
                        }
                        QPushButton:pressed {
                            background-color: #007bff;
                            color: white;
                        }
                    """)
                    odds_button.clicked.connect(lambda checked, opt=option, odd=odds: 
                                             self.odds_clicked.emit(self.title, f"{opt}: {odd}"))
                
                grid_layout.addWidget(odds_button, i // 2, i % 2)
        
        layout.addLayout(grid_layout)
        self.setLayout(layout)


class MatchItemWidget(QWidget):
    """自定义比赛项目组件"""
    
    def __init__(self, match_data):
        super().__init__()
        self.match_data = match_data
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 8, 10, 8)
        
        # 主信息行
        main_layout = QHBoxLayout()
        
        # 比赛ID和联赛
        id_league_layout = QVBoxLayout()
        
        # 处理不同的数据格式
        if 'gid' in self.match_data:
            # 真实API数据格式
            id_label = QLabel(f"ID: {self.match_data.get('gid', '')}")
            league_label = QLabel(self.match_data.get('league', ''))
            status = self.match_data.get('now_model', '')
            time_str = self.match_data.get('datetime', '')
            home_team = self.match_data.get('team_h', '')
            away_team = self.match_data.get('team_c', '')
            score_h = self.match_data.get('score_h', '0')
            score_c = self.match_data.get('score_c', '0')
            score = f"{score_h} - {score_c}"
        else:
            # 模拟数据格式
            id_label = QLabel(f"ID: {self.match_data.get('id', '')}")
            league_label = QLabel(self.match_data.get('league', ''))
            status = self.match_data.get('status', '')
            time_str = self.match_data.get('time', '')
            home_team = self.match_data.get('home_team', '')
            away_team = self.match_data.get('away_team', '')
            score = self.match_data.get('score', '0 - 0')
        
        id_label.setFont(QFont("Microsoft YaHei", 9, QFont.Weight.Bold))
        id_label.setStyleSheet("color: #007bff;")
        
        league_label.setFont(QFont("Microsoft YaHei", 8))
        league_label.setStyleSheet("color: #666;")
        
        id_league_layout.addWidget(id_label)
        id_league_layout.addWidget(league_label)
        main_layout.addLayout(id_league_layout)
        
        main_layout.addStretch()
        
        # 状态和时间
        status_time_layout = QVBoxLayout()
        status_label = QLabel(status)
        status_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        status_label.setFont(QFont("Microsoft YaHei", 8, QFont.Weight.Bold))
        
        # 根据状态设置颜色
        if status in ["HT", "进行中"]:
            status_label.setStyleSheet("color: #28a745;")
        elif status in ["FT", "已结束"]:
            status_label.setStyleSheet("color: #6c757d;")
        else:
            status_label.setStyleSheet("color: #ffc107;")
        
        time_label = QLabel(time_str)
        time_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        time_label.setFont(QFont("Microsoft YaHei", 8))
        time_label.setStyleSheet("color: #666;")
        
        status_time_layout.addWidget(status_label)
        status_time_layout.addWidget(time_label)
        main_layout.addLayout(status_time_layout)
        
        layout.addLayout(main_layout)
        
        # 球队信息行
        teams_layout = QHBoxLayout()
        
        home_team_label = QLabel(home_team)
        home_team_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        home_team_label.setStyleSheet("color: #333;")
        
        vs_label = QLabel("vs")
        vs_label.setFont(QFont("Microsoft YaHei", 9))
        vs_label.setStyleSheet("color: #666; margin: 0 10px;")
        
        away_team_label = QLabel(away_team)
        away_team_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        away_team_label.setStyleSheet("color: #333;")
        
        teams_layout.addWidget(home_team_label)
        teams_layout.addWidget(vs_label)
        teams_layout.addWidget(away_team_label)
        teams_layout.addStretch()
        
        # 比分
        score_label = QLabel(score)
        score_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        score_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        teams_layout.addWidget(score_label)
        
        layout.addLayout(teams_layout)
        
        self.setLayout(layout)
    
    def update_match_data(self, new_match_data):
        """更新比赛数据"""
        self.match_data = new_match_data
        
        # 更新所有标签的显示
        for child in self.children():
            if isinstance(child, QLabel):
                # 根据标签的文本内容来更新对应的数据
                if "ID:" in child.text():
                    if 'gid' in new_match_data:
                        child.setText(f"ID: {new_match_data.get('gid', '')}")
                    else:
                        child.setText(f"ID: {new_match_data.get('id', '')}")
                elif child.text() == self.match_data.get('league', ''):
                    child.setText(new_match_data.get('league', ''))
                elif child.text() == self.match_data.get('team_h', ''):
                    child.setText(new_match_data.get('team_h', ''))
                elif child.text() == self.match_data.get('away_team', ''):
                    child.setText(new_match_data.get('team_c', ''))
                elif child.text() == self.match_data.get('score', ''):
                    if 'gid' in new_match_data:
                        score = f"{new_match_data.get('score_h', '0')} - {new_match_data.get('score_c', '0')}"
                    else:
                        score = new_match_data.get('score', '0 - 0')
                    child.setText(score)
                elif child.text() == self.match_data.get('now_model', ''):
                    child.setText(new_match_data.get('now_model', ''))
                elif child.text() == self.match_data.get('datetime', ''):
                    child.setText(new_match_data.get('datetime', ''))


class MatchInfoWidget(QWidget):
    """比赛信息显示组件"""
    
    match_selected = pyqtSignal(dict)  # 信号：选中的比赛信息
    
    def __init__(self):
        super().__init__()
        self.matches_data = []
        self.filtered_matches = []
        self.init_ui()
    
    def get_matches_data(self):
        """获取比赛数据"""
        # 返回空列表，等待真实API数据
        return []
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 搜索区域
        search_layout = QHBoxLayout()
        
        search_label = QLabel("搜索:")
        search_label.setFont(QFont("Microsoft YaHei", 10))
        search_label.setStyleSheet("color: #333; margin-right: 5px;")
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入比赛ID、球队名或联赛名...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 11px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }
        """)
        self.search_input.textChanged.connect(self.filter_matches)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        
        # 添加总比赛数显示
        self.total_count_label = QLabel("总计: 0 场")
        self.total_count_label.setFont(QFont("Microsoft YaHei", 10))
        self.total_count_label.setStyleSheet("color: #007bff; font-weight: bold; margin-left: 10px;")
        search_layout.addWidget(self.total_count_label)
        
        layout.addLayout(search_layout)
        
        # 比赛列表标题
        title_layout = QHBoxLayout()
        title_label = QLabel("比赛列表")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #333;")
        
        count_label = QLabel(f"共 {len(self.matches_data)} 场比赛")
        count_label.setFont(QFont("Microsoft YaHei", 9))
        count_label.setStyleSheet("color: #666;")
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(count_label)
        layout.addLayout(title_layout)
        
        # 创建比赛列表
        self.match_list = QListWidget()
        self.match_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 5px;
                font-family: 'Microsoft YaHei';
                outline: none;
            }
            QListWidget::item {
                background-color: white;
                border: 1px solid #f8f9fa;
                border-radius: 6px;
                margin: 2px 0px;
                padding: 0px;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                border: 2px solid #007bff;
            }
            QListWidget::item:hover {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
            }
            QListWidget::item:alternate {
                background-color: #fafafa;
            }
        """)
        
        # 添加比赛到列表
        self.update_match_list()
        
        # 连接选择信号
        self.match_list.itemClicked.connect(self.on_match_selected)
        
        layout.addWidget(self.match_list)
        self.setLayout(layout)
    
    def filter_matches(self):
        """过滤比赛"""
        search_text = self.search_input.text().lower()
        
        if not search_text:
            self.filtered_matches = self.matches_data.copy()
        else:
            self.filtered_matches = []
            for match in self.matches_data:
                # 处理不同的数据格式
                if 'gid' in match:
                    # 真实API数据格式
                    match_id = match.get('gid', '').lower()
                    league = match.get('league', '').lower()
                    home_team = match.get('team_h', '').lower()
                    away_team = match.get('team_c', '').lower()
                else:
                    # 模拟数据格式
                    match_id = match.get('id', '').lower()
                    league = match.get('league', '').lower()
                    home_team = match.get('home_team', '').lower()
                    away_team = match.get('away_team', '').lower()
                
                if (search_text in match_id or
                    search_text in league or
                    search_text in home_team or
                    search_text in away_team):
                    self.filtered_matches.append(match)
        
        self.update_match_list()
    
    def filter_matches_by_text(self, matches_data, search_text):
        """根据搜索文本过滤比赛"""
        if not search_text:
            return matches_data.copy()
        
        filtered_matches = []
        for match in matches_data:
            # 处理不同的数据格式
            if 'gid' in match:
                # 真实API数据格式
                match_id = match.get('gid', '').lower()
                league = match.get('league', '').lower()
                home_team = match.get('team_h', '').lower()
                away_team = match.get('team_c', '').lower()
            else:
                # 模拟数据格式
                match_id = match.get('id', '').lower()
                league = match.get('league', '').lower()
                home_team = match.get('home_team', '').lower()
                away_team = match.get('away_team', '').lower()
            
            if (search_text in match_id or
                search_text in league or
                search_text in home_team or
                search_text in away_team):
                filtered_matches.append(match)
        
        return filtered_matches
    
    def update_match_list(self, matches_data=None, total_count=None):
        """更新比赛列表"""
        # 保存当前的搜索文本
        current_search_text = self.search_input.text() if hasattr(self, 'search_input') else ""
        
        if matches_data is not None:
            # 使用传入的真实比赛数据
            self.matches_data = matches_data
            
            # 如果有搜索文本，保持搜索状态
            if current_search_text.strip():
                self.filtered_matches = self.filter_matches_by_text(matches_data, current_search_text)
            else:
                self.filtered_matches = matches_data
        
        # 检查是否需要重新创建列表项
        need_rebuild = False
        if self.match_list.count() != len(self.filtered_matches):
            need_rebuild = True
        else:
            # 检查数据是否有变化
            for i in range(self.match_list.count()):
                item = self.match_list.item(i)
                if item:
                    current_data = item.data(Qt.ItemDataRole.UserRole)
                    if i < len(self.filtered_matches):
                        new_data = self.filtered_matches[i]
                        # 比较关键字段
                        if (current_data.get('gid', '') != new_data.get('gid', '') or
                            current_data.get('score_h', '') != new_data.get('score_h', '') or
                            current_data.get('score_c', '') != new_data.get('score_c', '')):
                            need_rebuild = True
                            break
        
        if need_rebuild:
            # 重新创建列表项
            self.match_list.clear()
            for match in self.filtered_matches:
                # 创建自定义项目组件
                item_widget = MatchItemWidget(match)
                
                # 创建列表项
                item = QListWidgetItem()
                item.setData(Qt.ItemDataRole.UserRole, match)
                
                # 设置项目大小
                item.setSizeHint(item_widget.sizeHint())
                
                # 添加到列表
                self.match_list.addItem(item)
                self.match_list.setItemWidget(item, item_widget)
        else:
            # 只更新现有项目的数据
            for i in range(self.match_list.count()):
                if i < len(self.filtered_matches):
                    item = self.match_list.item(i)
                    if item:
                        # 更新数据
                        item.setData(Qt.ItemDataRole.UserRole, self.filtered_matches[i])
                        # 更新显示组件
                        item_widget = self.match_list.itemWidget(item)
                        if item_widget:
                            item_widget.update_match_data(self.filtered_matches[i])
        
        # 更新计数
        if hasattr(self, 'count_label'):
            self.count_label.setText(f"共 {len(self.filtered_matches)} 场比赛")
        
        # 更新总比赛数
        if total_count is not None and hasattr(self, 'total_count_label'):
            self.total_count_label.setText(f"总计: {total_count} 场")
    
    def on_match_selected(self, item):
        """处理比赛选择事件"""
        match_data = item.data(Qt.ItemDataRole.UserRole)
        
        # 转换数据格式以适配现有的处理逻辑
        if 'gid' in match_data:
            # 真实API数据格式，转换为标准格式
            converted_data = {
                'id': match_data.get('gid', ''),
                'league': match_data.get('league', ''),
                'home_team': match_data.get('team_h', ''),
                'away_team': match_data.get('team_c', ''),
                'score': f"{match_data.get('score_h', '0')} - {match_data.get('score_c', '0')}",
                'status': match_data.get('now_model', ''),
                'time': match_data.get('datetime', ''),
                'retimeset': match_data.get('retimeset', ''),
                'strong': match_data.get('strong', ''),
                'ecid': match_data.get('ecid', ''),
                'game_id': match_data.get('game_id', ''),
                'lid': match_data.get('lid', ''),
                # 保留原始盘口数据
                'original_data': match_data
            }
        else:
            # 已经是标准格式
            converted_data = match_data
        
        self.match_selected.emit(converted_data)


class MatchOddsWidget(QWidget):
    """比赛盘口信息显示组件"""
    
    odds_clicked = pyqtSignal(str, str)  # 信号：盘口类型, 盘口值
    
    def __init__(self):
        super().__init__()
        self.current_match = None
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 比赛信息标题
        self.match_title = QLabel("请选择比赛查看盘口信息")
        self.match_title.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.match_title.setStyleSheet("color: #333; margin-bottom: 10px;")
        layout.addWidget(self.match_title)
        
        # 盘口信息区域
        self.odds_group = QGroupBox("盘口信息")
        self.odds_layout = QVBoxLayout()
        
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout()
        
        self.scroll_widget.setLayout(self.scroll_layout)
        self.scroll_area.setWidget(self.scroll_widget)
        self.scroll_area.setWidgetResizable(True)
        self.odds_layout.addWidget(self.scroll_area)
        self.odds_group.setLayout(self.odds_layout)
        
        layout.addWidget(self.odds_group)
        self.setLayout(layout)
    
    def update_match_odds(self, match_data):
        """更新比赛盘口信息"""
        self.current_match = match_data
        
        # 更新标题
        title_text = f"{match_data['home_team']} vs {match_data['away_team']} ({match_data['score']})"
        self.match_title.setText(title_text)
        
        # 清空现有盘口信息
        for i in reversed(range(self.scroll_layout.count())):
            self.scroll_layout.itemAt(i).widget().setParent(None)
        
        # 根据比赛ID生成不同的盘口数据
        match_id = match_data['id']
        
        # 如果有原始数据，直接使用
        if 'original_data' in match_data:
            odds_sections = self.get_odds_from_original_data(match_data['original_data'])
            print(f"从原始数据获取的盘口: {list(odds_sections.keys())}")
            # 如果有详细盘口数据，合并到盘口信息中
            if 'detailed_odds' in match_data:
                print(f"发现详细盘口数据: {match_data['detailed_odds']}")
                for section_name, section_odds in match_data['detailed_odds'].items():
                    odds_sections[section_name] = section_odds
                    print(f"添加详细盘口: {section_name} - {section_odds}")
            else:
                print("没有发现详细盘口数据")
        else:
            odds_sections = self.get_odds_by_match_id(match_id)
        
        print(f"更新盘口信息，共有 {len(odds_sections)} 个盘口组: {list(odds_sections.keys())}")
        for title, odds_data in odds_sections.items():
            print(f"创建盘口组件: {title} - {odds_data}")
            odds_widget = BettingOddsWidget(title, odds_data)
            odds_widget.odds_clicked.connect(self.on_odds_clicked)
            self.scroll_layout.addWidget(odds_widget)
    
    def get_odds_by_match_id(self, match_id):
        """根据比赛ID获取盘口数据"""
        # 从主窗口的比赛数据中查找对应的比赛
        if hasattr(self.parent(), 'parent') and hasattr(self.parent().parent(), 'matches_data'):
            main_window = self.parent().parent()
            for match in main_window.matches_data:
                if match.get('gid') == match_id:
                    # 提取盘口数据
                    odds_data = {}
                    
                    # 全场让球 - 主队和客队在一个组内
                    if match.get('ratio_re') and match.get('ior_reh') and match.get('ior_rec'):
                        odds_data["全场让球"] = {
                            "盘口": match['ratio_re'],
                            "主队": f"{match['ior_reh']}",
                            "客队": f"{match['ior_rec']}"
                        }
                    
                    # 全场大小 - 大和小在一个组内
                    if match.get('ratio_rouo') and match.get('ior_rouh') and match.get('ior_rouc'):
                        odds_data["全场大小"] = {
                            "盘口": match['ratio_rouo'],
                            "大": f"{match['ior_rouh']}",
                            "小": f"{match['ior_rouc']}"
                        }
                    
                    # 单双
                    if match.get('ior_reoo') and match.get('ior_reoe'):
                        odds_data["单双"] = {
                            "单": match['ior_reoo'],
                            "双": match['ior_reoe']
                        }
                    
                    # 双方球队进球
                    if match.get('ior_rtsy') and match.get('ior_rtsn'):
                        odds_data["双方球队进球"] = {
                            "是": match['ior_rtsy'],
                            "否": match['ior_rtsn']
                        }
                    
                    # 队伍1进球大小 - 大和小在一个组内
                    if match.get('ratio_rouho') and match.get('ior_rouho') and match.get('ior_rouhu'):
                        odds_data["队伍1进球大小"] = {
                            "盘口": match['ratio_rouho'],
                            "大": f"{match['ior_rouho']}",
                            "小": f"{match['ior_rouhu']}"
                        }
                    
                    # 队伍2进球大小 - 大和小在一个组内
                    if match.get('ratio_rouco') and match.get('ior_rouco') and match.get('ior_roucu'):
                        odds_data["队伍2进球大小"] = {
                            "盘口": match['ratio_rouco'],
                            "大": f"{match['ior_rouco']}",
                            "小": f"{match['ior_roucu']}"
                        }
                    
                    return odds_data
            
        # 如果没有找到数据，返回默认值
        return {
            "让球": {"暂无数据": "0.00"},
            "大小": {"暂无数据": "0.00"},
            "独赢": {"暂无数据": "0.00"}
        }
    
    def get_odds_from_original_data(self, original_data):
        """从原始数据中提取盘口信息"""
        odds_data = {}
        
        # 全场让球 - 主队和客队在一个组内
        ratio_re = original_data.get('ratio_re', '')
        ior_reh = original_data.get('ior_reh', '')
        ior_rec = original_data.get('ior_rec', '')
        
        if ratio_re and ior_reh and ior_rec and ratio_re != '' and ior_reh != '' and ior_rec != '':
            odds_data["全场让球"] = {
                "盘口": ratio_re,
                "主队": f"{ior_reh}",
                "客队": f"{ior_rec}"
            }
        
        # 全场大小 - 大和小在一个组内
        ratio_rouo = original_data.get('ratio_rouo', '')
        ior_rouh = original_data.get('ior_rouh', '')
        ior_rouc = original_data.get('ior_rouc', '')
        
        if ratio_rouo and ior_rouh and ior_rouc and ratio_rouo != '' and ior_rouh != '' and ior_rouc != '':
            odds_data["全场大小"] = {
                "盘口": ratio_rouo,
                "大": f"{ior_rouh}",
                "小": f"{ior_rouc}"
            }
        
        # 单双
        ior_reoo = original_data.get('ior_reoo', '')
        ior_reoe = original_data.get('ior_reoe', '')
        
        if ior_reoo and ior_reoe and ior_reoo != '' and ior_reoe != '':
            odds_data["单双"] = {
                "单": ior_reoo,
                "双": ior_reoe
            }
        
        # 双方球队进球
        ior_rtsy = original_data.get('ior_rtsy', '')
        ior_rtsn = original_data.get('ior_rtsn', '')
        
        if ior_rtsy and ior_rtsn and ior_rtsy != '' and ior_rtsn != '':
            odds_data["双方球队进球"] = {
                "是": ior_rtsy,
                "否": ior_rtsn
            }
        
        # 队伍1进球大小 - 大和小在一个组内
        ratio_rouho = original_data.get('ratio_rouho', '')
        ior_rouho = original_data.get('ior_rouho', '')
        ior_rouhu = original_data.get('ior_rouhu', '')
        
        if ratio_rouho and ior_rouho and ior_rouhu and ratio_rouho != '' and ior_rouho != '' and ior_rouhu != '':
            odds_data["队伍1进球大小"] = {
                "盘口": ratio_rouho,
                "大": f"{ior_rouho}",
                "小": f"{ior_rouhu}"
            }
        
        # 队伍2进球大小 - 大和小在一个组内
        ratio_rouco = original_data.get('ratio_rouco', '')
        ior_rouco = original_data.get('ior_rouco', '')
        ior_roucu = original_data.get('ior_roucu', '')
        
        if ratio_rouco and ior_rouco and ior_roucu and ratio_rouco != '' and ior_rouco != '' and ior_roucu != '':
            odds_data["队伍2进球大小"] = {
                "盘口": ratio_rouco,
                "大": f"{ior_rouco}",
                "小": f"{ior_roucu}"
            }
        
        # 注意：详细盘口信息不在original_data中，而是在match_data中
        # 这里不处理详细盘口，由update_match_odds方法处理
        
        return odds_data
    
    def on_odds_clicked(self, section, odds_info):
        """处理盘口点击事件"""
        print(f"点击了 {section} - {odds_info}")


class SettingsWidget(QWidget):
    """软件设置组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 基本设置
        basic_group = QGroupBox("基本设置")
        basic_layout = QGridLayout()
        
        settings = [
            ("自动刷新间隔(秒)", "30"),
            ("最大显示比赛数", "50")
        ]
        
        self.refresh_interval_edit = None
        self.max_matches_edit = None
        
        for i, (label, default_value) in enumerate(settings):
            basic_layout.addWidget(QLabel(label), i, 0)
            edit = QLineEdit(default_value)
            edit.setMaximumWidth(100)  # 设置输入框宽度
            edit.setStyleSheet("""
                QLineEdit {
                    padding: 5px;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                    font-size: 11px;
                }
            """)
            
            # 保存引用以便后续访问

            if label == "自动刷新间隔(秒)":
                self.refresh_interval_edit = edit
            elif label == "最大显示比赛数":
                self.max_matches_edit = edit
            
            basic_layout.addWidget(edit, i, 1)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # 高级设置
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QGridLayout()
        
        advanced_settings = [
            ("API超时时间(秒)", "10"),
            ("重试次数", "3"),
            ("日志级别", "INFO")
        ]
        
        for i, (label, default_value) in enumerate(advanced_settings):
            advanced_layout.addWidget(QLabel(label), i, 0)
            edit = QLineEdit(default_value)
            edit.setMaximumWidth(100)  # 设置输入框宽度
            edit.setStyleSheet("""
                QLineEdit {
                    padding: 5px;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                    font-size: 11px;
                }
            """)
            advanced_layout.addWidget(edit, i, 1)
        
        advanced_group.setLayout(advanced_layout)
        layout.addWidget(advanced_group)
        
        # 保存按钮
        save_button = QPushButton("保存设置")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_button.clicked.connect(self.save_settings)
        layout.addWidget(save_button)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def save_settings(self):
        """保存设置"""
        try:
            refresh_interval = int(self.refresh_interval_edit.text())
            max_matches = int(self.max_matches_edit.text())
            
            # 通知主窗口更新刷新间隔
            main_window = self.parent()
            if hasattr(main_window, 'update_refresh_interval'):
                main_window.update_refresh_interval(refresh_interval)
            
            # 保存到配置文件
            if hasattr(main_window, 'save_config'):
                main_window.save_config()
            
            print(f"设置已保存 - 刷新间隔: {refresh_interval}秒, 最大比赛数: {max_matches}")
            
        except ValueError:
            print("设置格式错误，请输入有效的数字")


class MatchDetailWidget(QWidget):
    """比赛详细信息显示组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("比赛详细信息")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #333; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 详细信息显示区域
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
            }
        """)
        layout.addWidget(self.detail_text)
        
        self.setLayout(layout)
    
    def update_detail(self, info):
        """更新详细信息"""
        self.detail_text.setText(info)


class CrownBettingTool(QMainWindow):
    """皇冠比赛数据显示工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.config_file = "Config.ini"
        self.start_time = None
        self.is_logged_in = False
        self.user_info = {}  # 存储用户信息
        self.matches_data = []  # 存储比赛数据
        self.refresh_interval = 30  # 默认刷新间隔30秒
        self.current_selected_match = None  # 当前选中的比赛
        self.detailed_odds_timer = None  # 详细盘口定时器
        self.init_ui()
        self.setup_timer()
        self.setup_status_bar()
        self.load_config()  # 程序启动时自动加载配置
    
    def init_ui(self):
        self.setWindowTitle("皇冠比赛数据显示工具")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 顶部登录区域
        self.create_login_section(main_layout)
        
        # 中间内容区域
        content_layout = QHBoxLayout()
        
        # 左侧Tab区域
        self.create_tab_section(content_layout)
        
        # 右侧详细信息区域
        self.create_detail_section(content_layout)
        
        main_layout.addLayout(content_layout)
        central_widget.setLayout(main_layout)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        

    
    def create_login_section(self, layout):
        """创建登录区域"""
        login_group = QGroupBox("账号登录")
        login_layout = QHBoxLayout()
        
        # 输入框 - 参考QT6.py的简洁配置
        self.inputs = {}
        
        # 账号
        login_layout.addWidget(QLabel("账号:"))
        self.inputs["账号:"] = QLineEdit()
        self.inputs["账号:"].setPlaceholderText("请输入账号")
        self.inputs["账号:"].setMaximumWidth(100)
        login_layout.addWidget(self.inputs["账号:"])
        
        # 密码
        login_layout.addWidget(QLabel("密码:"))
        self.inputs["密码:"] = QLineEdit()
        self.inputs["密码:"].setPlaceholderText("请输入密码")
        self.inputs["密码:"].setEchoMode(QLineEdit.EchoMode.Password)
        self.inputs["密码:"].setMaximumWidth(100)
        login_layout.addWidget(self.inputs["密码:"])
        
        # Ver
        login_layout.addWidget(QLabel("Ver:"))
        self.inputs["Ver:"] = QLineEdit()
        self.inputs["Ver:"].setPlaceholderText("请输入Ver")
        self.inputs["Ver:"].setMaximumWidth(150)
        login_layout.addWidget(self.inputs["Ver:"])
        
        # 域名
        login_layout.addWidget(QLabel("域名:"))
        self.inputs["域名:"] = QLineEdit()
        self.inputs["域名:"].setPlaceholderText("请输入域名")
        self.inputs["域名:"].setMaximumWidth(200)
        login_layout.addWidget(self.inputs["域名:"])
        
        # 按钮 - 放到同一行
        self.login_button = QPushButton("登录账号")
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        self.login_button.clicked.connect(self.login_account)
        
        self.monitor_button = QPushButton("开始监控")
        self.monitor_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.monitor_button.clicked.connect(self.start_monitoring)
        self.monitor_button.setEnabled(False)
        
        login_layout.addWidget(self.login_button)
        login_layout.addWidget(self.monitor_button)
        login_layout.addStretch()
        
        login_group.setLayout(login_layout)
        layout.addWidget(login_group)
    
    def create_tab_section(self, layout):
        """创建Tab区域"""
        self.tab_widget = QTabWidget()
        
        # 比赛信息Tab
        self.match_info_widget = MatchInfoWidget()
        self.match_info_widget.match_selected.connect(self.on_match_selected)
        self.tab_widget.addTab(self.match_info_widget, "比赛信息")
        
        # 软件设置Tab
        self.settings_widget = SettingsWidget()
        self.settings_widget.setParent(self)  # 设置父窗口引用
        self.tab_widget.addTab(self.settings_widget, "软件设置")
        
        # 重新加载配置到settings_widget
        self.load_settings_to_widget()
        
        layout.addWidget(self.tab_widget, 2)
    
    def create_detail_section(self, layout):
        """创建详细信息区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 盘口信息区域
        self.match_odds_widget = MatchOddsWidget()
        self.match_odds_widget.odds_clicked.connect(self.on_odds_clicked)
        splitter.addWidget(self.match_odds_widget)
        
        # 详细信息区域
        self.detail_widget = MatchDetailWidget()
        splitter.addWidget(self.detail_widget)
        
        # 设置分割器比例
        splitter.setSizes([600, 300])
        
        layout.addWidget(splitter, 1)
    
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.is_monitoring = False
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_bar)
        self.status_timer.start(1000)  # 每秒更新一次状态条
    
    def setup_status_bar(self):
        """设置状态条"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 运行时间标签
        self.runtime_label = QLabel("运行时间: 00:00:00")
        self.runtime_label.setStyleSheet("color: #333; font-weight: bold;")
        
        # 登录状态标签
        self.login_status_label = QLabel("登录状态: 未登录")
        self.login_status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        
        # 监控状态标签
        self.monitor_status_label = QLabel("监控状态: 未启动")
        self.monitor_status_label.setStyleSheet("color: #6c757d; font-weight: bold;")
        
        # 添加到状态条
        self.status_bar.addWidget(self.runtime_label)
        self.status_bar.addPermanentWidget(self.login_status_label)
        self.status_bar.addPermanentWidget(self.monitor_status_label)
        
        # 记录启动时间
        from datetime import datetime
        self.start_time = datetime.now()
    
    def update_status_bar(self):
        """更新状态条信息"""
        # 更新运行时间
        if self.start_time:
            from datetime import datetime
            elapsed = datetime.now() - self.start_time
            hours = elapsed.seconds // 3600
            minutes = (elapsed.seconds % 3600) // 60
            seconds = elapsed.seconds % 60
            self.runtime_label.setText(f"运行时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
        
        # 更新登录状态
        if self.is_logged_in:
            self.login_status_label.setText("登录状态: 已登录")
            self.login_status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.login_status_label.setText("登录状态: 未登录")
            self.login_status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        
        # 更新监控状态
        if self.is_monitoring:
            self.monitor_status_label.setText("监控状态: 运行中")
            self.monitor_status_label.setStyleSheet("color: #007bff; font-weight: bold;")
        else:
            self.monitor_status_label.setText("监控状态: 未启动")
            self.monitor_status_label.setStyleSheet("color: #6c757d; font-weight: bold;")
    
    def on_match_selected(self, match_data):
        """处理比赛选择事件"""
        # 停止之前的详细盘口定时器
        self.stop_detailed_odds_timer()
        
        # 保存当前选中的比赛
        self.current_selected_match = match_data
        
        # 更新盘口信息
        self.match_odds_widget.update_match_odds(match_data)
        
        # 更新详细信息
        detail_info = f"""
比赛ID: {match_data['id']}
联赛: {match_data['league']}
主队: {match_data['home_team']}
客队: {match_data['away_team']}
比分: {match_data['score']}
状态: {match_data['status']}
时间: {match_data['time']}

已选择比赛，正在加载盘口信息...
        """
        self.detail_widget.update_detail(detail_info)
        
        # 获取详细盘口信息
        self.get_detailed_odds(match_data)
        
        # 启动详细盘口定时器（每3秒获取一次）
        self.start_detailed_odds_timer()
    
    def get_detailed_odds(self, match_data):
        """获取详细盘口信息"""
        if not self.is_logged_in or not self.user_info:
            return
        
        # 获取lid和ecid
        lid = match_data.get('lid', '')
        ecid = match_data.get('ecid', '')
        
        if not lid or not ecid:
            print("缺少lid或ecid，无法获取详细盘口信息")
            return
        
        domain = self.inputs["域名:"].text().strip()
        if not domain:
            return
        
        # 启动获取详细盘口线程
        self.detailed_odds_thread = GetDetailedOddsThread(domain, self.user_info, lid, ecid, mobile_mode=False)
        self.detailed_odds_thread.detailed_odds_result.connect(self.on_detailed_odds_result)
        self.detailed_odds_thread.start()
    
    def on_detailed_odds_result(self, success, detailed_odds, error_msg):
        """处理详细盘口信息结果"""
        if success:
            print(f"获取详细盘口成功: {detailed_odds}")
            # 更新盘口信息，添加详细盘口
            if self.current_selected_match:
                # 将详细盘口数据添加到当前选中的比赛数据中
                self.current_selected_match['detailed_odds'] = detailed_odds
                # 更新盘口显示
                self.match_odds_widget.update_match_odds(self.current_selected_match)
                print(f"已更新盘口显示，详细盘口数据: {detailed_odds}")
            else:
                print("当前没有选中的比赛")
        else:
            print(f"获取详细盘口失败: {error_msg}")
    
    def start_detailed_odds_timer(self):
        """启动详细盘口定时器"""
        if self.detailed_odds_timer:
            self.detailed_odds_timer.stop()
        
        self.detailed_odds_timer = QTimer()
        self.detailed_odds_timer.timeout.connect(self.on_detailed_odds_timer_timeout)
        self.detailed_odds_timer.start(3000)  # 每3秒执行一次
        print("启动详细盘口定时器")
    
    def stop_detailed_odds_timer(self):
        """停止详细盘口定时器"""
        if self.detailed_odds_timer:
            self.detailed_odds_timer.stop()
            self.detailed_odds_timer = None
            print("停止详细盘口定时器")
    
    def on_detailed_odds_timer_timeout(self):
        """详细盘口定时器超时处理"""
        if self.current_selected_match and self.is_logged_in and self.is_monitoring:
            print("定时获取详细盘口信息...")
            self.get_detailed_odds(self.current_selected_match)
    
    def on_odds_clicked(self, section, odds_info):
        """处理盘口点击事件"""
        current_detail = self.detail_widget.detail_text.toPlainText()
        new_detail = f"{current_detail}\n\n点击了盘口: {section}\n盘口信息: {odds_info}"
        self.detail_widget.update_detail(new_detail)
    
    def login_account(self):
        """登录账号"""
        account = self.inputs["账号:"].text().strip()
        password = self.inputs["密码:"].text().strip()
        ver = self.inputs["Ver:"].text().strip()
        domain = self.inputs["域名:"].text().strip()
        
        if not all([account, password, ver, domain]):
            self.detail_widget.update_detail("请填写完整的登录信息")
            QMessageBox.warning(self, "登录失败", "请填写完整的登录信息")
            return
        
        # 禁用登录按钮
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        
        # 更新状态条
        self.login_status_label.setText("登录中...")
        self.login_status_label.setStyleSheet("color: #FF9800; font-weight: bold;")
        
        # 显示登录信息
        self.detail_widget.update_detail(f"正在登录...\n账号: {account}\n域名: {domain}")
        
        # 启动登录线程
        self.login_thread = LoginThread(domain, account, password, ver, mobile_mode=False)
        self.login_thread.login_result.connect(self.on_login_result)
        self.login_thread.start()
    
    def on_login_result(self, success, user_info, error_msg):
        """处理登录结果"""
        self.login_button.setEnabled(True)
        
        if success:
            self.user_info = user_info
            self.is_logged_in = True
            self.login_button.setText("已登录")
            self.login_button.setEnabled(False)
            
            # 更新状态条
            self.login_status_label.setText("登录状态: 已登录")
            self.login_status_label.setStyleSheet("color: #28a745; font-weight: bold;")
            
            # 启用监控按钮
            self.monitor_button.setEnabled(True)
            
            # 显示成功信息
            self.detail_widget.update_detail(f"登录成功！\nUID: {user_info['uid']}\n版本: {user_info['ver']}")
            
            # 保存配置
            self.save_config()
            
            QMessageBox.information(self, "登录成功", f"账号登录成功！\nUID: {user_info['uid']}")
        else:
            self.is_logged_in = False
            self.login_button.setText("登录账号")
            
            # 更新状态条
            self.login_status_label.setText("登录状态: 未登录")
            self.login_status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
            
            # 显示错误信息
            self.detail_widget.update_detail(f"登录失败: {error_msg}")
            
            QMessageBox.warning(self, "登录失败", f"登录失败: {error_msg}")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.is_logged_in:
            QMessageBox.warning(self, "提示", "请先登录账号")
            return
            
        if not self.is_monitoring:
            # 获取当前设置的刷新间隔
            if hasattr(self.settings_widget, 'refresh_interval_edit'):
                try:
                    interval = int(self.settings_widget.refresh_interval_edit.text())
                    if interval > 0:
                        self.refresh_interval = interval
                except ValueError:
                    pass  # 使用默认值
            
            # 立即获取一次比赛数据
            self.get_matches()
            
            # 使用配置的刷新间隔
            self.timer.start(self.refresh_interval * 1000)  # 转换为毫秒
            self.is_monitoring = True
            self.monitor_button.setText("停止监控")
            self.monitor_button.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            self.detail_widget.update_detail(f"监控已启动，刷新间隔: {self.refresh_interval}秒")
        else:
            self.timer.stop()
            # 停止详细盘口定时器
            self.stop_detailed_odds_timer()
            self.is_monitoring = False
            self.monitor_button.setText("开始监控")
            self.monitor_button.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            self.detail_widget.update_detail("监控已停止")
    
    def get_matches(self):
        """获取比赛列表"""
        if not self.is_logged_in or not self.user_info:
            return
        
        domain = self.inputs["域名:"].text().strip()
        if not domain:
            return
        
        # 启动获取比赛线程
        self.matches_thread = GetMatchesThread(domain, self.user_info, mobile_mode=False)
        self.matches_thread.matches_result.connect(self.on_matches_result)
        self.matches_thread.start()
    
    def on_matches_result(self, success, matches, error_msg, data_count):
        """处理获取比赛结果"""
        if success:
            self.matches_data = matches
            # 更新比赛列表显示
            self.match_info_widget.update_match_list(matches, data_count)
            
            # 如果当前有选中的比赛，更新盘口信息
            if self.current_selected_match:
                # 在更新后的数据中查找对应的比赛
                for match in matches:
                    if match.get('gid') == self.current_selected_match.get('id'):
                        # 更新选中的比赛数据，保留详细盘口数据
                        updated_match_data = {
                            'id': match.get('gid', ''),
                            'league': match.get('league', ''),
                            'home_team': match.get('team_h', ''),
                            'away_team': match.get('team_c', ''),
                            'score': f"{match.get('score_h', '0')} - {match.get('score_c', '0')}",
                            'status': match.get('now_model', ''),
                            'time': match.get('datetime', ''),
                            'retimeset': match.get('retimeset', ''),
                            'strong': match.get('strong', ''),
                            'ecid': match.get('ecid', ''),
                            'lid': match.get('lid', ''),
                            'game_id': match.get('game_id', ''),
                            'original_data': match
                        }
                        
                        # 保留之前的详细盘口数据
                        if hasattr(self.current_selected_match, 'detailed_odds') or 'detailed_odds' in self.current_selected_match:
                            updated_match_data['detailed_odds'] = self.current_selected_match.get('detailed_odds', {})
                            print(f"保留详细盘口数据: {updated_match_data['detailed_odds']}")
                        
                        # 更新盘口信息
                        self.match_odds_widget.update_match_odds(updated_match_data)
                        # 更新当前选中的比赛数据
                        self.current_selected_match = updated_match_data
                        break
            
            # 显示成功信息和更新时间
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.detail_widget.update_detail(f"成功获取 {len(matches)} 场比赛 (总计: {data_count} 场)\n\n最后更新: {current_time}")
        else:
            self.detail_widget.update_detail(f"获取比赛失败: {error_msg}")
    
    def update_data(self):
        """定时更新数据"""
        if self.is_monitoring and self.is_logged_in:
            # 显示正在更新的状态
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.detail_widget.update_detail(f"正在获取最新比赛数据...\n\n最后更新: {current_time}")
            
            # 获取比赛数据
            self.get_matches()
    
    def update_refresh_interval(self, new_interval):
        """更新刷新间隔"""
        if self.is_monitoring:
            # 如果正在监控，需要重启定时器
            self.timer.stop()
            self.refresh_interval = new_interval
            self.timer.start(self.refresh_interval * 1000)
            self.detail_widget.update_detail(f"刷新间隔已更新为: {self.refresh_interval}秒")
        else:
            self.refresh_interval = new_interval
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                config = configparser.ConfigParser()
                config.read(self.config_file, encoding='utf-8')
                
                if 'Login' in config:
                    # 加载登录信息
                    self.inputs["账号:"].setText(config.get('Login', 'account', fallback=''))
                    self.inputs["密码:"].setText(config.get('Login', 'password', fallback=''))
                    self.inputs["Ver:"].setText(config.get('Login', 'ver', fallback=''))
                    self.inputs["域名:"].setText(config.get('Login', 'domain', fallback=''))
                    
                if 'Settings' in config:
                    # 加载软件设置
                    try:
                        refresh_interval = config.get('Settings', 'refresh_interval', fallback='30')
                        max_matches = config.get('Settings', 'max_matches', fallback='50')
                        
                        # 如果settings_widget存在，直接设置
                        if hasattr(self, 'settings_widget'):
                            self.settings_widget.refresh_interval_edit.setText(refresh_interval)
                            self.settings_widget.max_matches_edit.setText(max_matches)
                        
                        # 更新刷新间隔
                        self.refresh_interval = int(refresh_interval)
                    except Exception as e:
                        print(f"加载软件设置失败: {e}")
                    
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def load_settings_to_widget(self):
        """加载设置到widget"""
        try:
            if os.path.exists(self.config_file):
                config = configparser.ConfigParser()
                config.read(self.config_file, encoding='utf-8')
                
                if 'Settings' in config and hasattr(self, 'settings_widget'):
                    try:
                        refresh_interval = config.get('Settings', 'refresh_interval', fallback='30')
                        max_matches = config.get('Settings', 'max_matches', fallback='50')
                        self.settings_widget.refresh_interval_edit.setText(refresh_interval)
                        self.settings_widget.max_matches_edit.setText(max_matches)
                        print(f"设置已加载 - 刷新间隔: {refresh_interval}秒, 最大比赛数: {max_matches}")
                    except Exception as e:
                        print(f"加载设置到widget失败: {e}")
        except Exception as e:
            print(f"加载设置到widget失败: {e}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            config = configparser.ConfigParser()
            
            # 保存登录信息
            config['Login'] = {
                'account': self.inputs["账号:"].text(),
                'password': self.inputs["密码:"].text(),
                'ver': self.inputs["Ver:"].text(),
                'domain': self.inputs["域名:"].text()
            }
            
            # 保存软件设置
            settings = {
                'window_width': str(self.width()),
                'window_height': str(self.height())
            }
            
            # 如果有设置组件，获取其设置值
            if hasattr(self, 'settings_widget'):
                try:
                    refresh_interval = self.settings_widget.refresh_interval_edit.text()
                    max_matches = self.settings_widget.max_matches_edit.text()
                    settings['refresh_interval'] = refresh_interval
                    settings['max_matches'] = max_matches
                except:
                    pass
            
            config['Settings'] = settings
            
            # 写入配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
                
            print(f"配置文件已保存到: {self.config_file}")
                
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def closeEvent(self, event):
        """程序关闭时自动保存配置"""
        self.stop_detailed_odds_timer()  # 停止详细盘口定时器
        self.save_config()
        event.accept()
    



def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion样式
    
    window = CrownBettingTool()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 