# 皇冠比赛数据显示工具 - 打包说明

## 📦 打包脚本

本目录包含两个打包脚本：

### 1. build_pyinstaller.bat (基础版本)
- 简单的打包脚本
- 适合快速打包测试

### 2. build_advanced.bat (高级版本) ⭐推荐
- 包含环境检查
- 自动安装依赖
- 详细的错误处理
- 优化选项
- 文件大小信息

## 🚀 使用方法

### 方法一：使用高级打包脚本（推荐）
```bash
# 双击运行
build_advanced.bat
```

### 方法二：使用基础打包脚本
```bash
# 双击运行
build_pyinstaller.bat
```

## 📋 打包前准备

1. **确保Python环境正确**
   - Python 3.7+
   - 已添加到PATH环境变量

2. **安装必要依赖**
   ```bash
   pip install pyinstaller
   pip install -r requirements.txt
   ```

3. **检查文件完整性**
   - Data.py (主程序)
   - Config.ini (配置文件)
   - requirements.txt (依赖列表)
   - README.md (说明文档)
   - icon.ico (图标文件)

## 🔧 打包选项说明

### 基础选项
- `--onefile`: 打包成单个可执行文件
- `--windowed`: 无控制台窗口
- `--name`: 指定输出文件名
- `--icon`: 指定程序图标

### 优化选项
- `--optimize=2`: 代码优化
- `--strip`: 二进制优化
- `--noconfirm`: 不询问确认

### 依赖处理
- `--hidden-import`: 隐藏导入的模块
- `--collect-all`: 收集所有相关文件
- `--copy-metadata`: 复制元数据

## 📁 输出文件

打包成功后，会在 `dist/` 目录下生成：
- `皇冠比赛数据显示工具.exe` - 主程序

## ⚠️ 常见问题

### 1. PyInstaller未找到
```bash
pip install pyinstaller
```

### 2. 依赖安装失败
```bash
pip install -r requirements.txt
```

### 3. 打包失败
- 检查Python版本兼容性
- 确保磁盘空间充足
- 尝试以管理员身份运行
- 检查杀毒软件设置

### 4. 文件过大
- 使用 `--strip` 选项
- 使用 `--optimize=2` 选项
- 检查不必要的依赖

## 🎯 最佳实践

1. **使用高级打包脚本**
   - 自动环境检查
   - 详细的错误信息
   - 优化选项

2. **定期清理**
   - 删除旧的dist和build目录
   - 清理临时文件

3. **测试打包结果**
   - 在干净的环境中测试
   - 检查所有功能是否正常

## 📞 技术支持

如果遇到问题，请检查：
- Python环境配置
- 依赖包版本
- 系统权限设置
- 磁盘空间

---

**注意**: 打包后的程序会自动创建配置文件，首次运行需要登录账号。 