('F:\\Code\\Py\\NewHG-Server\\Data\\build\\皇冠比赛数据显示工具\\PYZ-00.pyz',
 [('PyQt6',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('PyQt6.lupdate',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\__init__.py',
   'PYMODULE'),
  ('PyQt6.lupdate.designer_source',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\designer_source.py',
   'PYMODULE'),
  ('PyQt6.lupdate.lupdate',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\lupdate.py',
   'PYMODULE'),
  ('PyQt6.lupdate.pylupdate',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\pylupdate.py',
   'PYMODULE'),
  ('PyQt6.lupdate.python_source',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\python_source.py',
   'PYMODULE'),
  ('PyQt6.lupdate.source_file',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\source_file.py',
   'PYMODULE'),
  ('PyQt6.lupdate.translation_file',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\translation_file.py',
   'PYMODULE'),
  ('PyQt6.lupdate.translations',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\translations.py',
   'PYMODULE'),
  ('PyQt6.lupdate.user',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\lupdate\\user.py',
   'PYMODULE'),
  ('PyQt6.uic',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.as_string',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\as_string.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.compiler',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.indenter',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.misc',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.proxy_metaclass',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qobjectcreator',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qtproxies',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.loader',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.qobjectcreator',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.compile_ui',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\compile_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.enum_map',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\enum_map.py',
   'PYMODULE'),
  ('PyQt6.uic.exceptions',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\exceptions.py',
   'PYMODULE'),
  ('PyQt6.uic.icon_cache',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt6.uic.load_ui',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\load_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.objcreator',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.properties',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt6.uic.pyuic',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\pyuic.py',
   'PYMODULE'),
  ('PyQt6.uic.ui_file',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\ui_file.py',
   'PYMODULE'),
  ('PyQt6.uic.uiparser',
   'F:\\Python312\\Lib\\site-packages\\PyQt6\\uic\\uiparser.py',
   'PYMODULE'),
  ('__future__', 'F:\\Python312\\Lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'F:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'F:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'F:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'F:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'F:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'F:\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'F:\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'F:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'F:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'F:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'F:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'F:\\Python312\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'F:\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'F:\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'F:\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'F:\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'F:\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'F:\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'F:\\Python312\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'F:\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'F:\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'F:\\Python312\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'F:\\Python312\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'F:\\Python312\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'F:\\Python312\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'F:\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'F:\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'F:\\Python312\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'F:\\Python312\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'F:\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'F:\\Python312\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'F:\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'F:\\Python312\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'F:\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'F:\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'F:\\Python312\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'F:\\Python312\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'F:\\Python312\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'F:\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'F:\\Python312\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'F:\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'F:\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'F:\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'F:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'F:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'F:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'F:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'F:\\Python312\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.__main__',
   'F:\\Python312\\Lib\\site-packages\\certifi\\__main__.py',
   'PYMODULE'),
  ('certifi.core',
   'F:\\Python312\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'F:\\Python312\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'F:\\Python312\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'F:\\Python312\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'F:\\Python312\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'F:\\Python312\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'F:\\Python312\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'F:\\Python312\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'F:\\Python312\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('concurrent', 'F:\\Python312\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'F:\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'F:\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'F:\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'F:\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'F:\\Python312\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'F:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'F:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'F:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'F:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'F:\\Python312\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'F:\\Python312\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('dataclasses', 'F:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'F:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'F:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'F:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'F:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'F:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'F:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'F:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'F:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'F:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'F:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'F:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'F:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'F:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'F:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'F:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'F:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'F:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'F:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'F:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.mime', 'F:\\Python312\\Lib\\email\\mime\\__init__.py', 'PYMODULE'),
  ('email.mime.base', 'F:\\Python312\\Lib\\email\\mime\\base.py', 'PYMODULE'),
  ('email.mime.multipart',
   'F:\\Python312\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'F:\\Python312\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text', 'F:\\Python312\\Lib\\email\\mime\\text.py', 'PYMODULE'),
  ('email.parser', 'F:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'F:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'F:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'F:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'F:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'F:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'F:\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'F:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'F:\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'F:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'F:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'F:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'F:\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('http', 'F:\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'F:\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'F:\\Python312\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'F:\\Python312\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('idna', 'F:\\Python312\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'F:\\Python312\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'F:\\Python312\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'F:\\Python312\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'F:\\Python312\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'F:\\Python312\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'F:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'F:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'F:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'F:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'F:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'F:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'F:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'F:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'F:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'F:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'F:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'F:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'F:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'F:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'F:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'F:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'F:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'F:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'F:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'F:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'F:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'F:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'F:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'F:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'F:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'F:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'F:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'F:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'F:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'F:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'F:\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'F:\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'F:\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'F:\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'F:\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'F:\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'F:\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'F:\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'F:\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'F:\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'F:\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'F:\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'F:\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'F:\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'F:\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'F:\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'F:\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'F:\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'F:\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'F:\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'F:\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'F:\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'F:\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'F:\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'F:\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'F:\\Python312\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'F:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'F:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'F:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'F:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'F:\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'F:\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'F:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'F:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'F:\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'F:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'F:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'F:\\Python312\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'F:\\Python312\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'F:\\Python312\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'F:\\Python312\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'F:\\Python312\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'F:\\Python312\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'F:\\Python312\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'F:\\Python312\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'F:\\Python312\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'F:\\Python312\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.help',
   'F:\\Python312\\Lib\\site-packages\\requests\\help.py',
   'PYMODULE'),
  ('requests.hooks',
   'F:\\Python312\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'F:\\Python312\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'F:\\Python312\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'F:\\Python312\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'F:\\Python312\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'F:\\Python312\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'F:\\Python312\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'F:\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'F:\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'F:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'F:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'F:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('smtplib', 'F:\\Python312\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'F:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('socks', 'F:\\Python312\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('ssl', 'F:\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'F:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'F:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'F:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'F:\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'F:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'F:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'F:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'F:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'F:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'F:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'F:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'F:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'F:\\Python312\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib', 'F:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'F:\\Python312\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'F:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'F:\\Python312\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'F:\\Python312\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'F:\\Python312\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'F:\\Python312\\Lib\\uuid.py', 'PYMODULE'),
  ('xml', 'F:\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree', 'F:\\Python312\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'F:\\Python312\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'F:\\Python312\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'F:\\Python312\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'F:\\Python312\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'F:\\Python312\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'F:\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'F:\\Python312\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'F:\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'F:\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'F:\\Python312\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'F:\\Python312\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'F:\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'F:\\Python312\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'F:\\Python312\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'F:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'F:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'F:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'F:\\Python312\\Lib\\zipimport.py', 'PYMODULE'),
  ('zstandard',
   'F:\\Python312\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'F:\\Python312\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
