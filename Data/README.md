# 皇冠比赛数据显示工具

这是一个基于PyQt6开发的皇冠比赛数据显示工具，用于监控和显示比赛盘口信息。

## 功能特性

- **账号登录**: 支持账号、密码、Ver、域名输入
- **实时监控**: 可以开始/停止监控比赛数据
- **比赛信息**: 显示比赛基本信息和盘口数据
- **盘口点击**: 每个盘口信息都可以点击查看详情
- **软件设置**: 提供基本和高级设置选项
- **详细信息**: 右侧显示比赛详细信息和操作日志

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python Data.py
```

## 界面布局

1. **顶部区域**: 账号登录输入框和操作按钮
2. **左侧Tab**: 
   - 比赛信息: 显示比赛列表和盘口信息
   - 软件设置: 配置监控参数
3. **右侧区域**: 显示比赛详细信息和操作日志

## 盘口信息类型

- 总进球数
- 双方球队进球
- 双方球队进球 下半场
- 球队进球数 大/小
- 让球
- 大小
- 独赢
- 下个进球
- 单/双

## 使用说明

1. 填写账号、密码、Ver、域名信息
2. 点击"登录账号"按钮
3. 登录成功后点击"开始监控"
4. 在比赛信息中点击盘口查看详情
5. 在软件设置中配置监控参数

## 技术特点

- 使用PyQt6构建现代化界面
- 支持实时数据更新
- 响应式布局设计
- 美观的用户界面
- 完整的错误处理 