"""
图标辅助模块 - 统一管理应用程序图标
"""

import os
import sys
from PyQt6.QtGui import QIcon

def get_icon():
    """获取应用程序图标"""
    try:
        # 获取程序所在目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe文件
            base_path = sys._MEIPASS
        else:
            # 如果是开发环境
            base_path = os.path.dirname(os.path.abspath(__file__))
        
        # 尝试多个可能的图标路径
        icon_paths = [
            os.path.join(base_path, 'icon.ico'),
            os.path.join(os.path.dirname(base_path), 'icon.ico'),
            'icon.ico',
            ':/icon.ico'  # Qt资源文件
        ]
        
        for icon_path in icon_paths:
            if icon_path.startswith(':/') or os.path.exists(icon_path):
                icon = QIcon(icon_path)
                if not icon.isNull():
                    return icon
        
        # 如果所有路径都失败，返回空图标
        return QIcon()
        
    except Exception as e:
        print(f"加载图标失败: {e}")
        return QIcon()

def set_window_icon(window):
    """为窗口设置图标"""
    try:
        icon = get_icon()
        if not icon.isNull():
            window.setWindowIcon(icon)
    except Exception as e:
        print(f"设置窗口图标失败: {e}")

def set_app_icon(app):
    """为应用程序设置图标"""
    try:
        icon = get_icon()
        if not icon.isNull():
            app.setWindowIcon(icon)
    except Exception as e:
        print(f"设置应用程序图标失败: {e}") 