"""
关于对话框 - 显示作者信息和联系方式
"""

import sys
import base64
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap, QFont

class AboutDialog(QDialog):
    """关于对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("")  # 去掉标题
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)  # 无边框
        self.setFixedSize(500, 750)  # 增加高度以适应更高的图片
        self.setModal(True)
        
        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                border: 2px solid #ddd;
                border-radius: 10px;
            }
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
            }
            QLabel {
                color: #333;
                font-size: 14px;
                line-height: 1.5;
            }
        """)
        
        self.init_ui()
        self.center_dialog()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)  # 减小间距
        layout.setContentsMargins(20, 15, 20, 20)  # 减小上边距
        
        # 添加右上角关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: #999;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ff5757;
                color: white;
                border-radius: 15px;
            }
        """)
        close_btn.clicked.connect(self.accept)
        
        # 创建顶部布局，包含关闭按钮
        top_layout = QHBoxLayout()
        top_layout.addStretch()
        top_layout.addWidget(close_btn)
        top_layout.setContentsMargins(0, 5, 5, 0)
        layout.addLayout(top_layout)
        
        # 图片区域 - 在整个对话框中居中
        image_frame = QFrame()
        image_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        image_layout = QVBoxLayout(image_frame)
        image_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 布局居中
        image_layout.setContentsMargins(20, 20, 20, 20)  # 设置边距
        
        # 显示图片
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 尝试加载真实图片数据
        pixmap = self.load_author_image()
        image_label.setPixmap(pixmap)
        image_label.setScaledContents(False)  # 不强制拉伸，保持比例
        # 移除固定尺寸限制，让图片自然居中
        # image_label.setMaximumSize(320, 420)  
        # image_label.setMinimumSize(320, 420)  
        
        image_layout.addWidget(image_label, 0, Qt.AlignmentFlag.AlignCenter)  # 添加居中对齐
        layout.addWidget(image_frame, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 作者信息
        info_text = QLabel(
            "👨‍💻 <b>作者:</b> @BY0XCODE<br><br>"
            "🚀 <b>项目:</b> HG代理监控<br><br>"
            "💼 <b>专业领域:</b> 软件开发定制 & 系统监控<br><br>"
            "📱 <b>联系方式:</b> Telegram: @BY0XCODE<br><br>"
            "🎯 <b>版本:</b> 专业版 v1.0"
        )
        info_text.setAlignment(Qt.AlignmentFlag.AlignLeft)
        info_text.setWordWrap(True)
        info_text.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 14px;
                line-height: 1.8;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
            }
        """)
        layout.addWidget(info_text)
        
        self.setLayout(layout)
    
    def create_placeholder_image(self):
        """创建占位符图片"""
        # 创建一个简单的占位符图片
        pixmap = QPixmap(400, 200)
        pixmap.fill(Qt.GlobalColor.lightGray)
        return pixmap.scaled(400, 200, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
    
    def load_author_image(self):
        """加载作者图片"""
        try:
            # 尝试从多个可能的路径加载TG.png
            import sys
            import os
            
            possible_paths = []
            
            # 如果是打包后的程序
            if hasattr(sys, '_MEIPASS'):
                possible_paths.append(os.path.join(sys._MEIPASS, 'TG.png'))
            
            # 开发环境路径
            possible_paths.extend([
                'TG.png',
                os.path.join(os.path.dirname(__file__), 'TG.png'),
                os.path.join(os.getcwd(), 'TG.png')
            ])
            
            for path in possible_paths:
                if os.path.exists(path):
                    pixmap = QPixmap(path)
                    if not pixmap.isNull():
                        print(f"✅ 成功加载图片: {path}")
                        print(f"原始图片尺寸: {pixmap.width()}x{pixmap.height()}")
                        # 按比例缩放到合适大小，最大宽度400，最大高度400
                        if pixmap.width() > 400 or pixmap.height() > 400:
                            scaled_pixmap = pixmap.scaled(400, 400, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                        else:
                            scaled_pixmap = pixmap
                        print(f"缩放后尺寸: {scaled_pixmap.width()}x{scaled_pixmap.height()}")
                        return scaled_pixmap
            
            print("❌ 未找到TG.png图片文件")
            # 如果找不到图片，创建占位符
            return self.create_styled_placeholder()
            
        except Exception as e:
            print(f"❌ 加载图片时发生错误: {e}")
            return self.create_styled_placeholder()
    
    def create_styled_placeholder(self):
        """创建样式化的占位符"""
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.GlobalColor.white)
        
        # 这里可以添加一些简单的图形绘制
        from PyQt6.QtGui import QPainter, QPen, QBrush
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制边框
        pen = QPen(Qt.GlobalColor.lightGray)
        pen.setWidth(2)
        painter.setPen(pen)
        painter.drawRect(10, 10, 380, 280)
        
        # 绘制文字
        painter.setPen(Qt.GlobalColor.gray)
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "@BY0XCODE\nTG.png 图片未找到\n请确保图片已正确打包")
        
        painter.end()
        return pixmap
    
    def center_dialog(self):
        """居中显示对话框"""
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)
    
    def mousePressEvent(self, event):
        """鼠标按下事件 - 开始拖拽"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.globalPosition().toPoint()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 执行拖拽"""
        if hasattr(self, 'drag_start_position') and event.buttons() == Qt.MouseButton.LeftButton:
            delta = event.globalPosition().toPoint() - self.drag_start_position
            self.move(self.pos() + delta)
            self.drag_start_position = event.globalPosition().toPoint() 