@echo off
chcp 65001 >nul

echo 正在打包 HG跟单客户端...
echo.

REM 清理之前的打包文件
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "*.spec" del /q *.spec

REM PyInstaller打包命令 - 增强兼容性版本
pyinstaller ^
    --onefile ^
    --windowed ^
    --name=HG跟单客户端 ^
    --icon=icon.ico ^
    --add-data="icon.ico;." ^
    --hidden-import=PyQt6 ^
    --hidden-import=PyQt6.QtCore ^
    --hidden-import=PyQt6.QtGui ^
    --hidden-import=PyQt6.QtWidgets ^
    --hidden-import=PyQt6.sip ^
    --hidden-import=sip ^
    --hidden-import=requests ^
    --hidden-import=urllib3 ^
    --hidden-import=certifi ^
    --hidden-import=hashlib ^
    --hidden-import=uuid ^
    --hidden-import=platform ^
    --hidden-import=json ^
    --hidden-import=time ^
    --hidden-import=logging ^
    --hidden-import=traceback ^
    --hidden-import=configparser ^
    --hidden-import=threading ^
    --hidden-import=datetime ^
    --hidden-import=re ^
    --hidden-import=os ^
    --hidden-import=sys ^
    --collect-all=PyQt6 ^
    --collect-all=requests ^
    --collect-all=urllib3 ^
    --collect-all=certifi ^
    --collect-submodules=PyQt6 ^
    --copy-metadata=PyQt6 ^
    --copy-metadata=requests ^
    --copy-metadata=urllib3 ^
    --noconfirm ^
    V2.0.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 打包成功！
    echo 📁 输出目录: dist\
    echo 🚀 可执行文件: dist\HG监控跟单.exe
) else (
    echo.
    echo ❌ 打包失败！错误代码: %ERRORLEVEL%
)

pause 