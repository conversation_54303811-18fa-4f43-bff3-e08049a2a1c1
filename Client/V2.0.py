import sys
import re
import requests
import json
import time
import os
import configparser
import logging
from datetime import datetime
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
from urllib3 import disable_warnings
from urllib3.exceptions import InsecureRequestWarning
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# PyQt6 imports
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QComboBox, QLineEdit, QPushButton, QTabWidget,
    QTableWidget, QTableWidgetItem, QTextEdit, QGridLayout,
    QRadioButton, QMenu, QInputDialog, QMessageBox, QCheckBox,
    QSplitter, QGroupBox, QFrame, QProgressBar, QStatusBar, QDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, QMutex, QMutexLocker
from PyQt6.QtGui import QColor, QFont, QIcon, QAction, QPixmap

# 禁用SSL警告
disable_warnings(InsecureRequestWarning)

# 配置日志
def setup_logging():
    """配置日志系统"""
    log_filename = f"hg_client_{datetime.now().strftime('%Y%m%d')}.log"
    
    # 创建logger
    logger = logging.getLogger('HGClient')
    logger.setLevel(logging.INFO)
    
    # 避免重复添加handler
    if not logger.handlers:
        # 创建文件handler
        file_handler = logging.FileHandler(log_filename, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter('【%(asctime)s】>> %(message)s', 
                                    datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        
        # 添加handler到logger
        logger.addHandler(file_handler)
    
    return logger

# 初始化日志
request_logger = setup_logging()

def make_request(url, data=None, headers=None, method='POST', timeout=15, description=""):
    """
    统一的请求函数，用于记录详细日志
    
    Args:
        url: 请求URL
        data: 请求数据
        headers: 请求头
        method: 请求方法 (GET/POST)
        timeout: 超时时间
        description: 请求描述
    
    Returns:
        requests.Response对象
    """
    global request_logger
    
    # 记录请求开始
    request_logger.info(f"开始请求 - {description}")
    request_logger.info(f"请求URL: {url}")
    request_logger.info(f"请求方法: {method}")
    
    if data:
        # 隐藏敏感信息（密码、UID等）
        safe_data = str(data)
        # 替换敏感信息
        import re
        safe_data = re.sub(r'(password=)[^&]*', r'\1***', safe_data)
        safe_data = re.sub(r'(uid=)[^&]*', r'\1***', safe_data)
        request_logger.info(f"请求参数: {safe_data}")
    
    if headers:
        # 只记录关键头信息，避免过长
        key_headers = {k: v for k, v in headers.items() if k in ['Content-Type', 'User-Agent']}
        request_logger.info(f"请求头: {key_headers}")
    
    start_time = time.time()
    
    try:
        # 发送请求
        if method.upper() == 'GET':
            response = requests.get(url, params=data, headers=headers, timeout=timeout, verify=False)
        else:
            response = requests.post(url, data=data, headers=headers, timeout=timeout, verify=False)
        
        # 计算请求耗时
        elapsed_time = round((time.time() - start_time) * 1000, 2)
        
        # 记录响应信息
        request_logger.info(f"请求完成 - 状态码: {response.status_code}, 耗时: {elapsed_time}ms")
        
        # 记录响应内容（限制长度）
        response_text = response.text
        if len(response_text) > 1000:
            response_preview = response_text[:500] + "...[内容过长，已截断]..." + response_text[-200:]
        else:
            response_preview = response_text
        
        request_logger.info(f"响应内容: {response_preview}")
        
        # 检查HTTP状态
        response.raise_for_status()
        
        request_logger.info(f"请求成功完成 - {description}")
        request_logger.info("")
        return response
        
    except requests.exceptions.Timeout:
        request_logger.error(f"请求超时 - {description}, 超时时间: {timeout}s")
        raise
    except requests.exceptions.ConnectionError as e:
        request_logger.error(f"连接错误 - {description}, 错误: {str(e)}")
        raise
    except requests.exceptions.HTTPError as e:
        request_logger.error(f"HTTP错误 - {description}, 状态码: {response.status_code}, 错误: {str(e)}")
        raise
    except Exception as e:
        request_logger.error(f"请求异常 - {description}, 错误: {str(e)}")
        raise

# 内置域名列表
DOMAIN_LIST = [
    "https://mos066.com",
    "https://www.mos066.com",
    "https://mos055.com",
    "https://www.mos055.com",
    "https://mos033.com",
    "https://www.mos033.com",
    "https://mos022.com",
    "https://www.mos022.com",
    "https://mos011.com",
    "https://www.mos011.com",
    "https://hga050.com",
    "https://www.hga050.com",
    "https://hga039.com",
    "https://www.hga039.com",
    "https://hga038.com",
    "https://www.hga038.com",
    "https://hga035.com",
    "https://www.hga035.com",
    "https://hga030.com",
    "https://www.hga030.com",
    "https://hga026.com",
    "https://www.hga026.com",
    "https://hga050.com/"
]

# 错误代码映射表
ERROR_CODE_MAP = {
    "0X001": "由于网站流量较高，请重新再试。谢谢",
    "0X002": "由于网站流量较高，请重新再试。谢谢",
    "0X003": "系统正在忙碌中，请稍后再试。",
    "0X004": "系统正在忙碌中，请稍后再试。",
    "0X005": "系统正在忙碌中，请稍后再试。",
    "0X006": "系统最佳化中，请稍后。",
    "0X007": "系统正在忙碌中，请稍后再试。",
    "0X008": "系统正在忙碌中，请稍后再试。",
    "1X000": "此选项不再开放投注。请从交易单中移除。",
    "1X001": "此选项目前不开放投注。",
    "1X002": "已超过停止交易时间，无法进行交易。",
    "1X003": "本场次已转至走地盘口，请至走地交易。",
    "1X004": "最小投注金额为",
    "1X005": "让球数，赔率或比分已更新。",
    "1X006": "让球数，赔率或比分已更新。",
    "1X007": "输入比分后 1 分钟内，无法进行交易!!",
    "1X008": "交易金额不可大于股东单场总信用额度。请联系您的直属上线以解决这个问题。",
    "1X009": "暂时停止交易",
    "1X010": "暂时停止交易",
    "1X011": "此选项不再开放投注。请从交易单中移除。",
    "1X012": "您的总投注金额已超过您的余额，请重新编辑投注金额。",
    "1X013": "赔率错误，请重新交易。",
    "1X014": "登入失败，请重新尝试。",
    "1X015": "让球数，赔率或比分已更新。",
    "1X016": "让球数，赔率或比分已更新。",
    "1X017": "已超过某场次之过关注单限额",
    "1X018": "最高投注额设在 ",
    "1X019": "同组合可赢钱额不得超过人民币 ",
    "1X020": "单注最高可赢钱额： 人民币 ",
    "1X021": "让过赛事重覆",
    "1X022": "最小投注金额为",
    "1X023": "本场有下注金额最高是 ",
    "1X024": "下注失败，请重新交易。",
    "1X025": "球头错误。",
    "1X026": "所选赛事跨天。",
    "1X027": "过关串数错误",
    "1X029": "您没有足够的昨日余额进行昨日赛事的投注",
    "1X030": "本场有下注金额最高是 ",
    "1X031": "最高投注额设在 ",
    "1X032": "组合过关选项不足",
    "1X034": "被标注的选项无法串组合过关。",
    "1X035": "让球数，赔率或比分已更新。",
    "1X036": "本场有下注金额最高是 ",
    "1X037": "额度最优化中，请稍后再试。",
    "1X038": "系统正在忙碌中，请稍后再试。",
    "score_changed": "让球数，赔率或比分已更新。",
    "order_failed": "投注失败",
    "connectFail": "系统目前无法回应，请稍后再试。",
    "1more_failed": "至少一张注单投注失败。",
    "1more_place_failed": "至少一张注单无法投注。",
    "1more_pending": "至少一张注单目前正在处理中。",
    "1more_rejected": "至少一张注单被拒收。",
    "bet_success": "您已成功投注。",
    "bet_pending": "您的注单正在处理中，请稍候。",
    "bet_rejected": "您的注单已被拒收。",
    "1more_rejected_toast": "您有至少一张注单已被拒绝。请到\"交易状况\"查看详情",
    "bet_try_again": "由于网站流量较高，请重新再试。谢谢",
    "max": "为上限",
    "error_mem_max": "本场累积下注共计：",
    "error_mem_max1": "，总下注金额已超过单场限额。",
    "error7D": "过关选项的日期间距不可以超过7天。",
    "errorLogin": "现在我们的系统面临技术问题。请稍后再尝试登入。对于这样的不便我们深表抱歉，我们也正在全力的解决该问题。谢谢您的耐心等待。",
    "betError000": "交易失败，请重新交易。",
    "betError9487": "交易失败，请重新交易。",
    "betError878787": "网路不稳，请至交易状况确认注单是否成功。",
    "totalBet_close": "目前无法同时下注单注和过关的模式。",
    "betError016": "交易单处理中，请稍后....",
    "betError021": "不接受",
    "betError022": "串过关投注。",
    "str_over_sc": "交易金额已超过单场最高限额。",
    "user_stop": "您的帐户已被停用，请联络您的上线开启你的帐号。",
    "user_forbid": "您的帐号已被禁止登入，请联络您的上线开启你的帐号！",
    "4pwd_input": "输入密码",
    "4pwd_confirmed": "确认密码",
    "4pwd_title": "移除四位数密码",
    "4pwd_btn": "确认",
    "4pwd_removed": "您已成功移除四位数密码登入",
    "4pwd_removedAlready": "您已成功移除四位数密码登入",
    "4pwd_login_fail": "密码错误，请重新输入",
    "4pwd_fail_twice": "您已连续两次输入错误密码，请使用您的帐号密码登入。",
    "4pwd_doubleCheck_fail": "两次输入的四位数密码必须一致",
    "4pwd_block": "密码错误次数过多，请联络您的线上寻求协助。",
    "4pwd_db_fail": "基于安全考量，此设备/浏览器的四位数密码登入已被禁用。请使用您的帐号密码登入。",
    "4pwd_chg_pwd": "基于安全考量，在修改帐号密码后，四位数密码登入已被删除。请使用您的帐号密码登入。",
    "4pwd_fistcome": "设定您的简单密码以便快速登入。",
    "4pwd_onepassd": "重新输入你的简单密码来登入或是",
    "indexsubmit": "提交",
    "changepwd_oldpassword": "请输入现用密码。",
    "changepwd_password": "请输入新密码。",
    "changepwd_REpassword": "请输入确认密码。",
    "changepwd_oldpassworderror": "您输入的密码不正确，请重试。",
    "changepwd_passworderror": "您的新密码必须和现用密码不一样。",
    "changepwd_REpassworderror": "密码确认错误，请重新输入。",
    "changepwd_error": "1.您的新密码必须由 6-12 个字母和数字 (A-Z 或 0-9) 组成。\n2.您的新密码不能和现用密码相同",
    "changepwd_passwordcomplete": "你的密码已成功修改完成。",
    "changepwd_stop": "停止修改密码？",
    "rule_error_1": "您的新密码必须由 6-12 个字母和数字 (A-Z 或 0-9) 组成。",
    "rule_error_2": "您输入的密码不符合要求：<br>1. 您的新密码必须有6-12个字母和数字组合。<br>2. 三个不同的字母数字。<br>3. 不准许有空格"
}

def get_error_message(code):
    """获取错误代码对应的中文描述"""
    if not code:
        return ""
    
    # 移除可能的空白字符
    code = str(code).strip()
    
    # 查找错误代码对应的描述
    error_msg = ERROR_CODE_MAP.get(code, "")
    
    if error_msg:
        return f"[{code}] {error_msg}"
    else:
        return f"[{code}] 未知错误代码"

class MultiLineEditDialog(QDialog):
    """多行编辑对话框"""
    
    def __init__(self, parent=None, title="编辑", current_text="", placeholder="每行一个，支持多个"):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 300)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QTextEdit {
                background-color: white;
                border: 2px solid #E0E0E0;
                border-radius: 4px;
                font-size: 12px;
                padding: 8px;
            }
            QTextEdit:focus {
                border-color: #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QLabel {
                color: #333;
                font-size: 12px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 说明标签
        info_label = QLabel(f"请输入内容，{placeholder}")
        info_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(info_label)
        
        # 文本编辑区域
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText(placeholder)
        
        # 将当前文本转换为多行显示
        if current_text:
            # 将顿号分隔的文本转换为多行
            lines = current_text.split('、')
            self.text_edit.setPlainText('\n'.join(lines))
        
        layout.addWidget(self.text_edit)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.confirm_btn = QPushButton("确认")
        
        self.cancel_btn.setFixedWidth(80)
        self.confirm_btn.setFixedWidth(80)
        
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.confirm_btn)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.cancel_btn.clicked.connect(self.reject)
        self.confirm_btn.clicked.connect(self.accept)
        
        # 设置焦点
        self.text_edit.setFocus()
    
    def get_text(self):
        """获取编辑后的文本，返回顿号分隔的字符串"""
        lines = []
        for line in self.text_edit.toPlainText().split('\n'):
            line = line.strip()
            if line:  # 过滤空行
                lines.append(line)
        return '、'.join(lines)

# 主题配置
THEMES = {
    "默认主题": """
        QMainWindow {
            background-color: #f5f5f5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        QWidget {
            background-color: #f5f5f5;
            color: #333;
        }
        QPushButton {
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: #1976D2;
        }
        QPushButton:pressed {
            background-color: #0D47A1;
        }
        QPushButton:disabled {
            background-color: #BDBDBD;
            color: #757575;
        }
        QLineEdit {
            background-color: white;
            border: 2px solid #E0E0E0;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
        }
        QLineEdit:focus {
            border-color: #2196F3;
        }
        QComboBox {
            background-color: white;
            border: 2px solid #E0E0E0;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
        }
        QComboBox:focus {
            border-color: #2196F3;
        }
        QTabWidget::pane {
            border: 1px solid #E0E0E0;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #F5F5F5;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #2196F3;
        }
        QTableWidget {
            background-color: white;
            border: 1px solid #E0E0E0;
            gridline-color: #F0F0F0;
            selection-background-color: #E3F2FD;
        }
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #F0F0F0;
        }
        QTableWidget::item:selected {
            background-color: #E3F2FD;
            color: #1976D2;
        }
        QHeaderView::section {
            background-color: #F5F5F5;
            color: #333;
            padding: 8px;
            border: 1px solid #E0E0E0;
            font-weight: bold;
        }
        QHeaderView::section:hover {
            background-color: #E8E8E8;
        }
        QTextEdit {
            background-color: white;
            border: 1px solid #E0E0E0;
            border-radius: 4px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #E0E0E0;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QStatusBar {
            background-color: #FAFAFA;
            border-top: 1px solid #E0E0E0;
        }
    """,
    "深色主题": """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #106ebe;
        }
        QPushButton:pressed {
            background-color: #005a9e;
        }
        QLineEdit {
            background-color: #404040;
            border: 2px solid #555555;
            border-radius: 4px;
            padding: 6px 12px;
            color: white;
        }
        QLineEdit:focus {
            border-color: #0078d4;
        }
        QComboBox {
            background-color: #404040;
            border: 2px solid #555555;
            border-radius: 4px;
            padding: 6px 12px;
            color: white;
        }
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #404040;
        }
        QTabBar::tab {
            background-color: #353535;
            color: white;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #404040;
            border-bottom: 2px solid #0078d4;
        }
        QTableWidget {
            background-color: #404040;
            border: 1px solid #555555;
            gridline-color: #555555;
            color: white;
        }
        QTableWidget::item:selected {
            background-color: #0078d4;
        }
        QHeaderView::section {
            background-color: #353535;
            color: white;
            padding: 8px;
            border: 1px solid #555555;
            font-weight: bold;
        }
        QHeaderView::section:hover {
            background-color: #404040;
        }
        QTextEdit {
            background-color: #404040;
            border: 1px solid #555555;
            color: white;
            font-family: 'Consolas', monospace;
            font-size: 13px;
        }
        QGroupBox {
            color: white;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QStatusBar {
            background-color: #353535;
            border-top: 1px solid #555555;
            color: white;
        }
    """
}

class OrderMonitorThread(QThread):
    """订单监控线程"""
    order_received = pyqtSignal(dict)
    log_message = pyqtSignal(str)
    
    def __init__(self, service_api, processed_wids, min_order_filter=False, min_amount=10):
        super().__init__()
        self.service_api = service_api
        self.processed_wids = processed_wids
        self.min_order_filter = min_order_filter
        self.min_amount = min_amount
        self.is_running = False
        self.mutex = QMutex()
    
    def run(self):
        """运行监控循环"""
        self.is_running = True
        # self.log_message.emit("开始监控订单...")
        
        while self.is_running:
            try:
                self.check_orders()
                self.msleep(1500)  # 1.5秒检查一次
            except Exception as e:
                self.log_message.emit(f"监控异常: {str(e)}")
                self.msleep(1500)  # 异常时等待5秒
    
    def check_orders(self):
        """检查新订单"""
        try:
            url = f"{self.service_api}/orders"
            response = requests.get(url, timeout=5, verify=False)
            response.raise_for_status()
            
            orders = response.json()
            if not orders:
                return
            
            for order in orders:
                wid = order.get("Wid")
                if not wid:
                    continue
                
                # 使用互斥锁保护共享资源
                with QMutexLocker(self.mutex):
                    if wid in self.processed_wids:
                        continue
                    self.processed_wids.add(wid)
                
                # 检查订单时效性
                order_time = order.get("ts")
                if order_time and (time.time() - float(order_time)) > 30:
                    self.log_message.emit(f"订单 {wid} 超过30秒，标记为超时")
                    order["status"] = "已超时"
                    self.order_received.emit(order)
                    continue
                
                print(order)
                print("----------------打印订单金额----------------")
                print(float(order.get("OrderMoney", 0)))
                print("----------------打印订单金额----------------")


                # 小额订单过滤
                if self.min_order_filter:
                    print("---------------触发小额过滤-----------------")
                    print(order.get("OrderMoney", 0))
                    print(self.min_amount)
                    print("--------------------------------")
                    order_amount = float(order.get("OrderMoney", 0))
                    if order_amount < self.min_amount:
                        print("--------------------------------")
                        print(order_amount)
                        print("--------------------------------")

                        self.log_message.emit(f"订单 {wid} 金额 {order_amount} 小于最小金额 {self.min_amount}，已过滤")
                        order["status"] = "小额过滤"
                        self.order_received.emit(order)
                        continue
                
                # 正常订单
                order["status"] = "开始处理"
                self.order_received.emit(order)
                
        except requests.exceptions.RequestException as e:
            self.log_message.emit(f"获取订单失败: {str(e)}")
        except Exception as e:
            self.log_message.emit(f"处理订单异常: {str(e)}")
    
    def stop(self):
        """停止监控"""
        self.is_running = False
        self.log_message.emit("停止监控订单...")

class BettingThread(QThread):
    """下注处理线程"""
    betting_result = pyqtSignal(int, str, dict)  # row, status, result_data
    log_message = pyqtSignal(str)
    
    def __init__(self, order_data, row, domain, user_info, bet_amount, retry_count=20, retry_delay=3, auto_adjust_amount=True, mobile_mode=False):
        super().__init__()
        self.order_data = order_data
        self.row = row
        self.domain = domain
        self.user_info = user_info
        self.bet_amount = bet_amount
        self.retry_count = retry_count
        self.retry_delay = retry_delay
        self.auto_adjust_amount = auto_adjust_amount
        self.mobile_mode = mobile_mode
    
    def run(self):
        """执行下注流程"""
        try:
            # 首次下注尝试
            success, odds_result = self.get_order_status(self.order_data["AddData"])
            if not success:
                self.betting_result.emit(self.row, "查水失败", {})
                self.retry_betting()
                return
            
            self.log_message.emit("查水成功")
            self.betting_result.emit(self.row, "查水成功", {"odds": odds_result["ratio"]})
            
            # 检查并调整下注金额
            final_bet_amount = self.adjust_bet_amount(self.bet_amount, odds_result)
            if final_bet_amount is None:
                self.log_message.emit("下注金额超出限制且未启用自动调整，跳过下注")
                self.betting_result.emit(self.row, "金额超限跳过", {})
                return
            
            if self.place_bet(self.order_data, odds_result, final_bet_amount):
                self.log_message.emit("下单成功")
                result_data = {
                    "odds": odds_result["ioratio"],
                    "score": self.order_data["Score"],
                    "amount": final_bet_amount
                }
                self.betting_result.emit(self.row, "下单成功", result_data)
            else:
                self.log_message.emit("下单失败，开始补单")
                self.betting_result.emit(self.row, "下单失败", {"amount": final_bet_amount})
                self.retry_betting()
                
        except Exception as e:
            self.log_message.emit(f"下注异常: {str(e)}")
            self.betting_result.emit(self.row, "下注异常", {})
    
    def retry_betting(self):
        """补单重试逻辑"""
        for attempt in range(self.retry_count):
            try:
                self.log_message.emit(f"补单尝试 {attempt + 1}/{self.retry_count}")
                
                success, odds_result = self.get_order_status(self.order_data["AddData"])
                if not success:
                    self.log_message.emit(f"补单查水失败（尝试 {attempt + 1}）: {odds_result}")
                    self.betting_result.emit(self.row, "补单查水失败", {})
                    self.msleep(int(self.retry_delay * 1000))
                    continue
                
                self.log_message.emit(f"补单查水成功（尝试 {attempt + 1}）")
                self.betting_result.emit(self.row, "补单查水成功", {"odds": odds_result["ratio"]})
                
                # 检查并调整下注金额
                final_bet_amount = self.adjust_bet_amount(self.bet_amount, odds_result)
                if final_bet_amount is None:
                    self.log_message.emit(f"补单金额超出限制且未启用自动调整，跳过补单（尝试 {attempt + 1}）")
                    if attempt == self.retry_count - 1:
                        self.betting_result.emit(self.row, "补单金额超限", {})
                    self.msleep(int(self.retry_delay * 1000))
                    continue
                
                if self.place_bet(self.order_data, odds_result, final_bet_amount):
                    self.log_message.emit(f"补单成功（尝试 {attempt + 1}）")
                    result_data = {
                        "odds": odds_result["ioratio"],
                        "score": self.order_data["Score"],
                        "amount": final_bet_amount
                    }
                    self.betting_result.emit(self.row, "补单成功", result_data)
                    return
                else:
                    self.log_message.emit(f"补单失败（尝试 {attempt + 1}）")
                    if attempt == self.retry_count - 1:
                        self.betting_result.emit(self.row, "补单失败", {"amount": final_bet_amount})
                    
                self.msleep(int(self.retry_delay * 1000))
                
            except Exception as e:
                self.log_message.emit(f"补单异常（尝试 {attempt + 1}）: {str(e)}")
                if attempt == self.retry_count - 1:
                    self.betting_result.emit(self.row, "补单异常", {})
    
    def get_order_status(self, add_data):
        """获取订单状态（查水）"""
        try:
            if not self.domain.startswith(("http://", "https://")):
                domain = f"https://{self.domain}"
            else:
                domain = self.domain
            
            url = f"{domain}/transform.php"
            params = {"ver": self.user_info["ver"]}
            
            payload = (
                f"p=FT_order_view"
                f"&uid={self.user_info['uid']}"
                f"&ver={self.user_info['ver']}"
                f"&langx=zh-cn"
                f"&odd_f_type=H"
                f"&gid={add_data['gid']}"
                f"&gtype={add_data['gtype']}"
                f"&wtype={add_data['wtype']}"
                f"&chose_team={add_data['chose_team']}"
            )
            
            # 根据手机版模式设置User-Agent
            if self.mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent,
            }
            
            response = make_request(url, payload, headers, timeout=10, description="查水获取赔率")
            
            root = ET.fromstring(response.text)
            code = root.find("code").text
            
            if code != "501":
                error_msg = get_error_message(root.find("errormsg").text)
                return False, f"查水失败:{error_msg}"
            
            def get_text(element, default=""):
                found = root.find(element)
                return found.text if found is not None else default
            
            odds_data = {
                "score": get_text("score", ""),
                "spread": get_text("spread"),
                "rtype": get_text("fast_check"),
                "ts": get_text("ts"),
                "match_info": f"{get_text('league_name')} {get_text('team_name_h')} vs {get_text('team_name_c')}",
                "ratio": get_text("ratio"),
                "ptype": get_text("ptype"),
                "con": get_text("con"),
                "ioratio": get_text("ioratio"),
                "max_gold": get_text("gold_gmax"),
                "min_gold": get_text("gold_gmin")
            }
            
            return True, odds_data
            
        except Exception as e:
            return False, f"查水异常: {str(e)}"
    
    def place_bet(self, item, odds_result, bet_amount_str):
        """执行下注"""
        try:
            if not self.domain.startswith(("http://", "https://")):
                domain = f"https://{self.domain}"
            else:
                domain = self.domain
            
            url = f"{domain}/transform.php"
            params = {"ver": self.user_info["ver"]}
            
            ts_value = odds_result["ts"] if odds_result["ts"] is not None else ""
            ptype_value = odds_result["ptype"] if odds_result["ptype"] is not None else ""
            if item['AddData']['chose_team'] == "RTSY":
                rtype = "RTSY"
            elif item['AddData']['chose_team'] == "TSY":
                rtype = "TSY"
            elif item['AddData']['chose_team'] == "TSN":
                rtype = "TSN"    
            elif item['AddData']['chose_team'] == "RTSN":
                rtype = "RTSN"
            elif item['AddData']['wtype'] == "RT" or item['AddData']['wtype'] == "T":
                rtype = item['AddData']['chose_team']
            else:
                rtype = item['AddData']['wtype'] + item['AddData']['chose_team']
            payload = (
                f"p=FT_bet"
                f"&uid={self.user_info['uid']}"
                f"&ver={self.user_info['ver']}"
                f"&langx=zh-cn"
                f"&odd_f_type=H"
                f"&golds={bet_amount_str}"
                f"&gid={item['AddData']['gid']}"
                f"&gtype={item['AddData']['gtype']}"
                f"&wtype={item['AddData']['wtype']}"
                f"&rtype={rtype}"
                f"&chose_team={item['AddData']['chose_team']}"
                f"&ioratio={odds_result['ioratio']}"
                f"&con={odds_result['con']}"
                f"&ratio={odds_result['ratio']}"
                f"&autoOdd=Y"
                f"&timestamp={int(time.time() * 1000)}"
                f"&timestamp2={ts_value}"
                f"&isRB=Y"
                f"&imp=Y"
                f"&ptype={ptype_value}"
                f"&isYesterday=N"
                f"&f=1R"
            )
            
            # 根据手机版模式设置User-Agent
            if self.mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent,
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive"
            }
            
            response = make_request(url, payload, headers, timeout=15, description="执行下注")
            
            root = ET.fromstring(response.text)
            code = root.find("code").text
            
            # 记录下注结果和错误信息
            if str(code) == "560":
                return True
            else:
                error_msg = get_error_message(code = root.find("errormsg").text)
                self.log_message.emit(f"下注失败 {error_msg}")
                return False
            
        except Exception as e:
            self.log_message.emit(f"下注异常: {str(e)}")
            return False
    
    def adjust_bet_amount(self, bet_amount, odds_result):
        """调整下注金额，如果超出限制则自动调整或返回None"""
        try:
            bet_amount_int = int(float(bet_amount))
            
            # 获取最大和最小金额限制
            max_gold = odds_result.get("max_gold", "")
            min_gold = odds_result.get("min_gold", "")
            
            # 检查最小金额限制
            if min_gold and min_gold.isdigit():
                min_amount = int(min_gold)
                if bet_amount_int < min_amount:
                    if self.auto_adjust_amount:
                        self.log_message.emit(f"下注金额 {bet_amount_int} 小于最小限制 {min_amount}，自动调整为 {min_amount}")
                        bet_amount_int = min_amount
                    else:
                        self.log_message.emit(f"下注金额 {bet_amount_int} 小于最小限制 {min_amount}，跳过下注")
                        return None
            
            # 检查最大金额限制
            if max_gold and max_gold.isdigit():
                max_amount = int(max_gold)
                if bet_amount_int > max_amount:
                    if self.auto_adjust_amount:
                        self.log_message.emit(f"下注金额 {bet_amount_int} 超过最大限制 {max_amount}，自动调整为 {max_amount}")
                        bet_amount_int = max_amount
                    else:
                        self.log_message.emit(f"下注金额 {bet_amount_int} 超过最大限制 {max_amount}，跳过下注")
                        return None
            
            return str(bet_amount_int)
            
        except Exception as e:
            self.log_message.emit(f"金额调整异常: {str(e)}")
            return bet_amount  # 异常时返回原金额

class BalanceCheckThread(QThread):
    """余额检查线程"""
    balance_updated = pyqtSignal(str, str)  # balance, pending
    pending_orders_updated = pyqtSignal(list, str, int)  # orders_list, total_gold, count
    login_required = pyqtSignal()
    log_message = pyqtSignal(str)
    relogin_required = pyqtSignal()  # 新增信号
    
    def __init__(self, domain, user_info, mobile_mode=False):
        super().__init__()
        self.domain = domain
        self.user_info = user_info
        self.mobile_mode = mobile_mode
        self.is_running = False
        self.failure_count = 0
    
    def run(self):
        """运行余额检查循环"""
        self.is_running = True
        
        while self.is_running:
            try:
                if self.check_balance():
                    self.failure_count = 0
                else:
                    self.failure_count += 1
                    if self.failure_count >= 2:
                        self.log_message.emit("余额检查连续失败，可能需要重新登录")
                        self.login_required.emit()
                        self.failure_count = 0
                
                self.msleep(10000)  # 10秒检查一次
                
            except Exception as e:
                self.log_message.emit(f"余额检查异常: {str(e)}")
                self.msleep(10000)
    
    def check_balance(self):
        """检查余额和未结订单"""
        try:
            if not self.user_info.get("uid") or not self.user_info.get("ver"):
                return False
            
            if not self.domain.startswith(("http://", "https://")):
                domain = f"https://{self.domain}"
            else:
                domain = self.domain
            
            # 根据手机版模式设置User-Agent
            if self.mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            
            # 获取余额
            url = f"{domain}/transform.php?ver={self.user_info['ver']}"
            data = f"p=get_member_data&uid={self.user_info['uid']}&ver={self.user_info['ver']}&langx=zh-cn&change=credit"
            
            response = make_request(url, data, headers, timeout=10, description="获取账户余额")
            root = ET.fromstring(response.text)
            # print("获取余额结果",response.text)
            msg_element = root.find("msg")
            msg = msg_element.text if msg_element is not None else None
            # print("msg",msg)
            if msg == "doubleLogin":
                self.log_message.emit("检测到账号异地登录，正在自动重新登录...")
                self.relogin_required.emit()
                return False
            
            balance = root.find('maxcredit').text  
            if balance == "":
                return False
            
            ts = str(int(time.time() * 1000))
            data = (
                f"p=get_today_wagers&uid={self.user_info['uid']}&langx=zh-cn"
                f"&LS=g&selGtype=ALL&chk_cw=N&ts={ts}&format=json&db_slow=N"
            )
            
            response = make_request(url, data, headers, timeout=15, description="获取未结订单(余额检查)")
            
            pending_data = response.json()
            
            # 提取未结订单列表
            orders_list = []
            for item in pending_data.get("wagers", []):
                if item.get("result"):
                    orders_list.append(item)
            
            # 获取统计信息
            total_gold = pending_data.get("amout_gold", "0")
            order_count = pending_data.get("count", 0)
            
            # 发射信号更新余额和未结订单
            self.balance_updated.emit(balance, total_gold)
            self.pending_orders_updated.emit(orders_list, total_gold, order_count)
            
            return True
            
        except Exception as e:
            self.log_message.emit(f"余额检查失败: {str(e)}")
            return False
    
    def extract_text_between(self, text, start_tag, end_tag):
        """提取文本"""
        try:
            start = text.index(start_tag) + len(start_tag)
            end = text.index(end_tag, start)
            return text[start:end]
        except ValueError:
            return ""
    
    def stop(self):
        """停止余额检查"""
        self.is_running = False

class LoginThread(QThread):
    login_result = pyqtSignal(bool, dict, str)  # 是否成功, 用户信息, 错误信息

    def __init__(self, domain, username, password, ver, mobile_mode=False):
        super().__init__()
        self.domain = domain
        self.username = username
        self.password = password
        self.ver = ver
        self.mobile_mode = mobile_mode

    def run(self):
        try:
            domain = self.domain
            username = self.username
            password = self.password
            ver = self.ver
            if not domain.startswith(("http://", "https://")):
                domain = f"https://{domain}"
            login_url = f"{domain}/transform.php"
            
            # 根据手机版模式设置不同的参数
            if self.mobile_mode:
                app_param = "Y"  # 手机版使用app=Y
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                app_param = "N"  # 桌面版使用app=N
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            
            payload = (
                f"p=chk_login"
                f"&langx=zh-cn"
                f"&ver={ver}"
                f"&username={username}"
                f"&password={password}"
                f"&app={app_param}&auto=IHGCFD&blackbox="
                f"&userAgent=TW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMC4wLjAuMCBTYWZhcmkvNTM3LjM2"
            )
            headers = {
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            response = make_request(login_url, payload, headers, timeout=15, description="用户登录")
            root = ET.fromstring(response.text)
            status = root.find("status")
            if status is not None and status.text == "200":
                uid_element = root.find("uid")
                if uid_element is not None:
                    user_info = {"uid": uid_element.text, "ver": ver}
                    self.login_result.emit(True, user_info, "")
                    return
                else:
                    self.login_result.emit(False, {}, "登录响应中未找到UID")
                    return
            else:
                status_text = status.text if status is not None else "未知"
                error_msg = get_error_message(status_text)
                self.login_result.emit(False, {}, f"登录失败 {error_msg}")
                return
        except Exception as e:
            self.login_result.emit(False, {}, f"登录异常: {str(e)}")

class ProfitNotificationThread(QThread):
    """盈利推送线程"""
    profit_updated = pyqtSignal(str, str, str)  # 订单总额, 总有效金额, 总输赢
    log_message = pyqtSignal(str)
    
    def __init__(self, domain, user_info, mobile_mode=False, interval_minutes=30):
        super().__init__()
        self.domain = domain
        self.user_info = user_info
        self.mobile_mode = mobile_mode
        self.interval_minutes = interval_minutes
        self.is_running = False
    
    def run(self):
        """运行盈利推送循环"""
        self.is_running = True
        self.log_message.emit(f"开始盈利推送监控，间隔: {self.interval_minutes} 分钟")
        
        while self.is_running:
            try:
                self.check_profit()
                # 转换为毫秒
                self.msleep(self.interval_minutes * 60 * 1000)
            except Exception as e:
                self.log_message.emit(f"盈利推送异常: {str(e)}")
                self.msleep(60 * 1000)  # 异常时等待1分钟
    
    def check_profit(self):
        """检查盈利并推送"""
        try:
            if not self.user_info.get("uid") or not self.user_info.get("ver"):
                return
            
            if not self.domain.startswith(("http://", "https://")):
                domain = f"https://{self.domain}"
            else:
                domain = self.domain
            
            # 根据手机版模式设置User-Agent
            if self.mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
            
            url = f"{domain}/transform.php?ver={self.user_info['ver']}"
            data = f"p=get_history_data&uid={self.user_info['uid']}&langx=zh-cn&gtype=ALL&isAll=N&startdate=&enddate=&filter=Y"
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            
            response = make_request(url, data, headers, timeout=15, description="获取盈利数据")
            root = ET.fromstring(response.text)

            code = root.find("code").text
            if code != "607":
                error_msg = get_error_message(code)
                self.log_message.emit(f"获取盈利数据失败: {error_msg}")
                return
            
            # 获取统计信息
            total_gold = root.find("total_gold").text
            total_vgold = root.find("total_vgold").text
            total_winloss = root.find("total_winloss").text
            
            # 发射信号更新盈利信息
            self.profit_updated.emit(total_gold, total_vgold, total_winloss)
            
        except Exception as e:
            self.log_message.emit(f"检查盈利失败: {str(e)}")
    
    def stop(self):
        """停止盈利推送"""
        self.is_running = False
        self.log_message.emit("停止盈利推送监控")

class ReloginThread(QThread):
    relogin_completed = pyqtSignal(bool, dict)  # 成功/失败, user_info

    def __init__(self, domain, username, password, ver, mobile_mode=False):
        super().__init__()
        self.domain = domain
        self.username = username
        self.password = password
        self.ver = ver
        self.mobile_mode = mobile_mode

    def run(self):
        import xml.etree.ElementTree as ET
        try:
            domain = self.domain
            username = self.username
            password = self.password
            ver = self.ver
            if not domain.startswith(("http://", "https://")):
                domain = f"https://{domain}"
            login_url = f"{domain}/transform.php"
            
            # 根据手机版模式设置不同的参数
            if self.mobile_mode:
                app_param = "Y"  # 手机版使用app=Y
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                app_param = "N"  # 桌面版使用app=N
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            
            payload = (
                f"p=chk_login"
                f"&langx=zh-cn"
                f"&ver={ver}"
                f"&username={username}"
                f"&password={password}"
                f"&app={app_param}&auto=AZAZDC&blackbox=&userAgent=TW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMC4wLjAuMCBTYWZhcmkvNTM3LjM2"
            )
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            response = make_request(login_url, payload, headers, timeout=15, description="自动重新登录")
            root = ET.fromstring(response.text)
            status = root.find("status")
            if status is not None and status.text == "200":
                uid_element = root.find("uid")
                if uid_element is not None:
                    user_info = {"uid": uid_element.text, "ver": ver}
                    self.relogin_completed.emit(True, user_info)
                    return
            self.relogin_completed.emit(False, {})
        except Exception as e:
            print("自动重新登录失败", e)
            self.relogin_completed.emit(False, {})

class HGBettingClient(QMainWindow):
    """HG投注客户端主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("HG-FootBall-Betting V2.0 - Enhanced Edition")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化日志
        self.setup_logging()
        
        # 初始化变量
        self.user_info = {"ver": "", "uid": ""}
        self.is_monitoring = False
        self.processed_wids = set()
        self.start_time = None
        self.order_data = {}
        
        # 线程管理
        self.monitor_thread = None
        self.balance_thread = None
        self.betting_threads = []
        self.profit_thread = None
        
        # 初始化UI
        self.setup_ui()
        self.setup_connections()
        
        # 加载配置
        self.load_config()
        self.load_processed_wids()
        
        # 启动定时器
        self.setup_timers()
        
        self.log("HG投注客户端 V2.0 启动完成")
    
    def setup_logging(self):
        """设置日志"""
        log_filename = f'betting_{datetime.now().strftime("%Y%m%d")}.log'
        logging.basicConfig(
            filename=log_filename,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            encoding='utf-8'
        )
        self.logger = logging.getLogger('HGBettingClient')
    
    def setup_ui(self):
        """设置UI界面"""
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 顶部控制区域
        self.setup_top_controls(main_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        main_layout.addWidget(splitter)
        
        # 标签页
        self.setup_tabs(splitter)
        
        # 日志区域
        self.setup_log_area(splitter)
        
        # 状态栏
        self.setup_status_bar()
        
        # 设置分割器比例
        splitter.setSizes([550, 250])
    
    def setup_top_controls(self, layout):
        """设置顶部控制区域"""
        top_group = QGroupBox("连接配置")
        top_layout = QHBoxLayout(top_group)
        top_layout.setSpacing(15)
        
        # 主题选择
        self.theme_label = QLabel("主题:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(THEMES.keys())
        self.theme_combo.setFixedWidth(120)
        
        # 账号信息
        self.account_label = QLabel("账号:")
        self.account_input = QLineEdit()
        self.account_input.setFixedWidth(120)
        self.account_input.setPlaceholderText("输入账号")
        
        self.password_label = QLabel("密码:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedWidth(120)
        self.password_input.setPlaceholderText("输入密码")
        
        self.ver_label = QLabel("Ver:")
        self.ver_input = QLineEdit()
        self.ver_input.setFixedWidth(120)
        self.ver_input.setPlaceholderText("版本号")
        
        self.domain_label = QLabel("域名:")
        self.domain_input = QComboBox()
        self.domain_input.setFixedWidth(250)
        self.domain_input.setEditable(True)  # 允许用户输入自定义域名
        self.domain_input.addItems([""] + DOMAIN_LIST)  # 添加空选项和域名列表
        self.domain_input.setCurrentText("")  # 默认为空
        self.domain_input.lineEdit().setPlaceholderText("选择或输入域名")
        
        # 反向投注复选框
        self.reverse_betting_checkbox = QCheckBox("反向投注")
        self.reverse_betting_checkbox.setToolTip("启用反向投注模式，与原始订单方向相反")
        
        # 手机版复选框
        self.mobile_mode_checkbox = QCheckBox("手机版")
        self.mobile_mode_checkbox.setToolTip("启用手机版模式，使用手机版User-Agent和登录参数")
        
        # 控制按钮
        self.login_btn = QPushButton("登录账号")
        self.start_btn = QPushButton("开始监控")
        self.clear_btn = QPushButton("清空日志")
        
        for btn in [self.login_btn, self.start_btn, self.clear_btn]:
            btn.setFixedWidth(100)
        
        # 状态标签
        self.login_status_label = QLabel("未登录")
        self.login_status_label.setStyleSheet("color: #757575; font-weight: bold;")
        
        # 添加到布局
        widgets = [
            self.theme_label, self.theme_combo,
            self.account_label, self.account_input,
            self.password_label, self.password_input,
            self.ver_label, self.ver_input,
            self.domain_label, self.domain_input,
            self.reverse_betting_checkbox,  # 添加反向投注复选框
            self.mobile_mode_checkbox,  # 添加手机版复选框
            self.login_btn, self.start_btn, self.clear_btn,
            self.login_status_label
        ]
        
        for widget in widgets:
            top_layout.addWidget(widget)
        
        top_layout.addStretch()
        layout.addWidget(top_group)
    
    def setup_tabs(self, parent):
        """设置标签页"""
        self.tabs = QTabWidget()
        parent.addWidget(self.tabs)
        
        # 订单列表
        self.setup_order_tab()
        
        # 未结订单
        self.setup_pending_orders_tab()
        
        # 订单历史
        self.setup_history_tab()
        
        # 历史明细
        self.setup_history_details_tab()
        
        # 软件配置
        self.setup_config_tab()
    
    def setup_order_tab(self):
        """设置订单列表标签页"""
        order_widget = QWidget()
        order_layout = QVBoxLayout(order_widget)
        
        # 订单表格
        self.order_table = QTableWidget()
        self.order_table.setColumnCount(9)
        self.order_table.setHorizontalHeaderLabels([
            "比赛信息", "下单类型", "下单玩法", "下单盘口", 
            "下单金额", "下单比分", "下单赔率", "下单时间", "下单状态"
        ])
        
        # 设置列宽
        column_widths = [300, 100, 120, 120, 100, 100, 100, 150, 120]
        for i, width in enumerate(column_widths):
            self.order_table.setColumnWidth(i, width)
        
        self.order_table.setAlternatingRowColors(True)
        self.order_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.order_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.order_table.customContextMenuRequested.connect(self.show_order_context_menu)
        
        order_layout.addWidget(self.order_table)
        self.tabs.addTab(order_widget, "📋 订单列表")
    
    def setup_pending_orders_tab(self):
        """设置未结订单标签页"""
        pending_widget = QWidget()
        pending_layout = QVBoxLayout(pending_widget)
        
        # 控制按钮
        btn_layout = QHBoxLayout()
        self.load_pending_btn = QPushButton("🔄 加载未结订单")
        self.load_pending_btn.setFixedWidth(150)
        btn_layout.addWidget(self.load_pending_btn)
        btn_layout.addStretch()
        
        # 统计信息
        stats_layout = QHBoxLayout()
        self.pending_stats_total = QLabel("下单总额: 0")
        self.pending_stats_count = QLabel("订单数: 0")
        self.pending_stats_total.setStyleSheet("font-weight: bold; color: #2196F3;")
        self.pending_stats_count.setStyleSheet("font-weight: bold; color: #4CAF50;")
        
        stats_layout.addWidget(self.pending_stats_total)
        stats_layout.addWidget(self.pending_stats_count)
        stats_layout.addStretch()
        
        btn_layout.addLayout(stats_layout)
        pending_layout.addLayout(btn_layout)
        
        # 未结订单表格
        self.pending_order_table = QTableWidget()
        self.pending_order_table.setColumnCount(8)
        self.pending_order_table.setHorizontalHeaderLabels([
            "比赛类型", "订单号", "比赛信息", "下单比分", 
            "下单盘口", "下单金额", "下单赔率", "订单时间"
        ])
        
        column_widths = [100, 150, 350, 100, 150, 100, 100, 150]
        for i, width in enumerate(column_widths):
            self.pending_order_table.setColumnWidth(i, width)
        
        self.pending_order_table.setAlternatingRowColors(True)
        pending_layout.addWidget(self.pending_order_table)
        
        self.tabs.addTab(pending_widget, "⏳ 未结订单")
    
    def setup_history_tab(self):
        """设置历史订单标签页"""
        history_widget = QWidget()
        history_layout = QVBoxLayout(history_widget)
        
        # 控制按钮和统计
        btn_layout = QHBoxLayout()
        self.load_history_btn = QPushButton("📊 加载历史订单")
        self.load_history_btn.setFixedWidth(150)
        btn_layout.addWidget(self.load_history_btn)
        btn_layout.addStretch()
        
        # 历史统计
        stats_layout = QHBoxLayout()
        self.history_stats_total_gold = QLabel("订单总额: 0")
        self.history_stats_total_vgold = QLabel("总有效金额: 0")
        self.history_stats_total_winloss = QLabel("总输赢: 0")
        
        for label in [self.history_stats_total_gold, self.history_stats_total_vgold, self.history_stats_total_winloss]:
            label.setStyleSheet("font-weight: bold; color: #FF9800;")
        
        stats_layout.addWidget(self.history_stats_total_gold)
        stats_layout.addWidget(self.history_stats_total_vgold)
        stats_layout.addWidget(self.history_stats_total_winloss)
        stats_layout.addStretch()
        
        btn_layout.addLayout(stats_layout)
        history_layout.addLayout(btn_layout)
        
        # 历史订单表格
        self.order_history_table = QTableWidget()
        self.order_history_table.setColumnCount(5)
        self.order_history_table.setHorizontalHeaderLabels([
            "日期", "下单金额", "有效金额", "赢/输", "操作"
        ])
        
        column_widths = [150, 100, 100, 100, 80]
        for i, width in enumerate(column_widths):
            self.order_history_table.setColumnWidth(i, width)
        
        self.order_history_table.setAlternatingRowColors(True)
        history_layout.addWidget(self.order_history_table)
        
        self.tabs.addTab(history_widget, "📈 订单历史")
    
    def setup_history_details_tab(self):
        """设置历史明细标签页"""
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        
        # 历史明细表格
        self.history_details_table = QTableWidget()
        self.history_details_table.setColumnCount(9)
        self.history_details_table.setHorizontalHeaderLabels([
            "比赛类型", "订单号", "比赛信息", "下单盘口", 
            "下单赔率", "下单金额", "赛果比分", "订单状态", "输赢"
        ])
        
        # 设置列宽
        column_widths = [100, 150, 300, 150, 100, 100, 100, 100, 100]
        for i, width in enumerate(column_widths):
            self.history_details_table.setColumnWidth(i, width)
        
        self.history_details_table.setAlternatingRowColors(True)
        self.history_details_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        details_layout.addWidget(self.history_details_table)
        self.tabs.addTab(details_widget, "📋 历史明细")
    
    def setup_config_tab(self):
        """设置配置标签页"""
        config_widget = QWidget()
        config_layout = QHBoxLayout(config_widget)  # 改为水平布局
        config_layout.setSpacing(20)
        
        # 左侧：下注设置
        betting_group = QGroupBox("下注设置")
        betting_layout = QGridLayout(betting_group)
        betting_layout.setColumnStretch(0, 0)  # 标签列不拉伸
        betting_layout.setColumnStretch(1, 1)  # 输入框列可拉伸
        betting_layout.setHorizontalSpacing(10)  # 设置水平间距
        betting_layout.setVerticalSpacing(8)  # 设置统一的垂直间距
        
        # 下注配置项
        betting_config_items = [
            ("补单次数:", "retry_input", "20", "重试下注的次数"),
            ("补单延迟:", "delay_input", "3", "每次重试的间隔秒数"),
            ("登录检测:", "login_check_input", "30", "登录状态检测间隔"),
            ("下单金额:", "bet_amount_input", "50", "每次下注的金额"),
            ("服务API:", "service_api_input", "", "订单数据API地址"),
            ("最小金额:", "min_order_amount_input", "10", "小额订单过滤阈值")
        ]
        
        for i, (label_text, attr, default, tooltip) in enumerate(betting_config_items):
            label = QLabel(label_text)
            label.setToolTip(tooltip)
            
            input_field = QLineEdit()
            input_field.setText(default)
            input_field.setToolTip(tooltip)
            input_field.setFixedHeight(30)  # 设置统一高度
            
            if attr == "service_api_input":
                input_field.setFixedWidth(250)
            else:
                input_field.setFixedWidth(120)
            
            setattr(self, attr, input_field)
            
            betting_layout.addWidget(label, i, 0)
            
            if label_text == "下单金额:":
                amount_widget = QWidget()
                amount_widget.setFixedHeight(30)  # 设置固定高度与普通输入框一致
                amount_layout = QHBoxLayout(amount_widget)
                amount_layout.setContentsMargins(0, 0, 0, 0)
                amount_layout.setSpacing(8)  # 设置组件间距
                
                self.fixed_amount_radio = QRadioButton("固定金额")
                self.percent_amount_radio = QRadioButton("百分比")
                self.fixed_amount_radio.setChecked(True)
                
                amount_layout.addWidget(input_field)
                amount_layout.addWidget(self.fixed_amount_radio)
                amount_layout.addWidget(self.percent_amount_radio)
                amount_layout.addStretch()
                
                betting_layout.addWidget(amount_widget, i, 1)
            elif label_text == "最小金额:":
                filter_widget = QWidget()
                filter_widget.setFixedHeight(30)  # 设置固定高度与普通输入框一致
                filter_layout = QHBoxLayout(filter_widget)
                filter_layout.setContentsMargins(0, 0, 0, 0)
                filter_layout.setSpacing(8)  # 设置组件间距
                
                self.enable_min_order_filter = QCheckBox("启用过滤")
                filter_layout.addWidget(input_field)
                filter_layout.addWidget(self.enable_min_order_filter)
                filter_layout.addStretch()
                
                betting_layout.addWidget(filter_widget, i, 1)
            else:
                betting_layout.addWidget(input_field, i, 1)
        
        # 尾数过滤设置
        tail_filter_label = QLabel("尾数过滤:")
        betting_layout.addWidget(tail_filter_label, len(betting_config_items), 0)
        
        tail_filter_widget = QWidget()
        tail_filter_widget.setFixedHeight(30)  # 设置固定高度与普通输入框一致
        tail_filter_layout = QHBoxLayout(tail_filter_widget)
        tail_filter_layout.setContentsMargins(0, 0, 0, 0)
        tail_filter_layout.setSpacing(8)  # 设置组件间距
        
        self.tail_filter_input = QLineEdit()
        self.tail_filter_input.setText("0")
        self.tail_filter_input.setToolTip("下单金额尾数，如设置为5则跳过金额尾数为5的订单")
        self.tail_filter_input.setFixedWidth(120)
        
        self.enable_tail_filter = QCheckBox("启用过滤")
        self.enable_tail_filter.setToolTip("启用尾数过滤功能")
        
        tail_filter_layout.addWidget(self.tail_filter_input)
        tail_filter_layout.addWidget(self.enable_tail_filter)
        tail_filter_layout.addStretch()
        
        betting_layout.addWidget(tail_filter_widget, len(betting_config_items), 1)
        
        # 自动下单金额
        auto_bet_label = QLabel("自动下单:")
        betting_layout.addWidget(auto_bet_label, len(betting_config_items) + 1, 0)
        
        auto_bet_widget = QWidget()
        auto_bet_widget.setFixedHeight(30)  # 设置固定高度与普通输入框一致
        auto_bet_layout = QHBoxLayout(auto_bet_widget)
        auto_bet_layout.setContentsMargins(0, 0, 0, 0)
        
        self.auto_bet_amount_checkbox = QCheckBox("自动下单金额")
        self.auto_bet_amount_checkbox.setChecked(True)  # 默认勾选
        self.auto_bet_amount_checkbox.setToolTip(
            "如果下单金额大于注单的最大下单金额，则自动使用最大金额下注。\n"
            "未勾选则不下单。"
        )
        
        auto_bet_layout.addWidget(self.auto_bet_amount_checkbox)
        auto_bet_layout.addStretch()
        
        betting_layout.addWidget(auto_bet_widget, len(betting_config_items) + 1, 1)
        
        # 右侧：通知设置
        notification_group = QGroupBox("通知设置")
        notification_layout = QGridLayout(notification_group)
        notification_layout.setColumnStretch(0, 0)  # 标签列不拉伸
        notification_layout.setColumnStretch(1, 1)  # 输入框列可拉伸
        notification_layout.setHorizontalSpacing(10)  # 设置水平间距
        notification_layout.setVerticalSpacing(8)  # 设置统一的垂直间距
        
        # 通知配置项
        notification_config_items = [
            ("微信通知:", "wechat_uid_input", "", "微信推送UID列表", True),
            ("收件地址:", "email_address_input", "", "接收通知的邮箱地址列表", True),
            ("SMTP服务器:", "smtp_server_input", "smtp.qq.com", "邮件服务器地址", False),
            ("SMTP端口:", "smtp_port_input", "587", "邮件服务器端口", False),
            ("发送邮箱:", "sender_email_input", "", "发送通知的邮箱账号", False),
            ("邮箱密码:", "email_password_input", "", "邮箱授权码或密码", False)
        ]
        
        for i, (label_text, attr, default, tooltip, need_edit_btn) in enumerate(notification_config_items):
            label = QLabel(label_text)
            label.setToolTip(tooltip)
            
            if need_edit_btn:
                # 需要编辑按钮的字段
                input_widget = QWidget()
                input_layout = QHBoxLayout(input_widget)
                input_layout.setContentsMargins(0, 0, 0, 0)
                input_layout.setSpacing(5)  # 设置间距
                
                input_field = QLineEdit()
                input_field.setText(default)
                input_field.setToolTip(tooltip)
                input_field.setReadOnly(True)  # 设置为只读
                input_field.setFixedWidth(165)  # 调整宽度与普通字段保持一致
                
                edit_btn = QPushButton("编辑")
                edit_btn.setFixedWidth(30)  # 稍微缩小按钮宽度
                edit_btn.setToolTip(f"编辑{label_text.replace(':', '')}")
                
                # 连接编辑按钮事件
                if attr == "wechat_uid_input":
                    edit_btn.clicked.connect(self.edit_wechat_uids)
                elif attr == "email_address_input":
                    edit_btn.clicked.connect(self.edit_email_addresses)
                
                input_layout.addWidget(input_field)
                input_layout.addWidget(edit_btn)
                input_layout.addStretch()  # 添加弹性空间保持对齐
                
                setattr(self, attr, input_field)
                setattr(self, f"{attr}_edit_btn", edit_btn)
                
                notification_layout.addWidget(label, i, 0)
                notification_layout.addWidget(input_widget, i, 1)
            else:
                # 普通输入字段
                input_field = QLineEdit()
                input_field.setText(default)
                input_field.setToolTip(tooltip)
                input_field.setFixedWidth(200)
                
                # 密码字段设置为密码模式
                if "密码" in label_text:
                    input_field.setEchoMode(QLineEdit.EchoMode.Password)
                
                setattr(self, attr, input_field)
                
                notification_layout.addWidget(label, i, 0)
                notification_layout.addWidget(input_field, i, 1)
        
        # 通知开关
        notification_switches_label = QLabel("通知开关:")
        notification_layout.addWidget(notification_switches_label, len(notification_config_items), 0)
        
        switches_widget = QWidget()
        switches_layout = QHBoxLayout(switches_widget)  # 改为水平布局
        switches_layout.setContentsMargins(0, 0, 0, 0)
        switches_layout.setSpacing(20)  # 设置两个复选框之间的间距
        
        self.enable_wechat_notification = QCheckBox("启用微信通知")
        self.enable_email_notification = QCheckBox("启用邮箱通知")
        self.enable_profit_notification = QCheckBox("推送盈利")
        
        self.enable_wechat_notification.setToolTip("启用微信推送通知")
        self.enable_email_notification.setToolTip("启用邮箱推送通知")
        self.enable_profit_notification.setToolTip("启用盈利推送通知")
        
        switches_layout.addWidget(self.enable_wechat_notification)
        switches_layout.addWidget(self.enable_email_notification)
        switches_layout.addWidget(self.enable_profit_notification)
        switches_layout.addStretch()  # 添加弹性空间，让复选框左对齐
        
        notification_layout.addWidget(switches_widget, len(notification_config_items), 1)
        
        # 推送间隔设置
        profit_interval_label = QLabel("推送间隔:")
        notification_layout.addWidget(profit_interval_label, len(notification_config_items) + 1, 0)
        
        profit_interval_widget = QWidget()
        profit_interval_layout = QHBoxLayout(profit_interval_widget)
        profit_interval_layout.setContentsMargins(0, 0, 0, 0)
        profit_interval_layout.setSpacing(10)
        
        self.profit_interval_input = QLineEdit()
        self.profit_interval_input.setText("30")
        self.profit_interval_input.setFixedWidth(100)
        self.profit_interval_input.setPlaceholderText("分钟")
        self.profit_interval_input.setToolTip("盈利推送的间隔时间（分钟）")
        
        profit_interval_layout.addWidget(self.profit_interval_input)
        profit_interval_layout.addWidget(QLabel("分钟"))
        profit_interval_layout.addStretch()
        
        notification_layout.addWidget(profit_interval_widget, len(notification_config_items) + 1, 1)
        
        # 保存引用以便后续控制显示/隐藏
        self.profit_interval_widget = profit_interval_widget
        self.profit_interval_label = profit_interval_label
        
        # 初始状态：根据复选框状态控制显示
        self.profit_interval_widget.setVisible(False)
        self.profit_interval_label.setVisible(False)
        
        # 测试按钮
        test_buttons_label = QLabel("测试通知:")
        notification_layout.addWidget(test_buttons_label, len(notification_config_items) + 2, 0)
        
        test_buttons_widget = QWidget()
        test_buttons_layout = QHBoxLayout(test_buttons_widget)
        test_buttons_layout.setContentsMargins(0, 0, 0, 0)
        test_buttons_layout.setSpacing(10)  # 设置按钮间距
        
        self.test_wechat_btn = QPushButton("测试微信")
        self.test_email_btn = QPushButton("测试邮箱")
        
        self.test_wechat_btn.setFixedWidth(80)
        self.test_email_btn.setFixedWidth(80)
        
        test_buttons_layout.addWidget(self.test_wechat_btn)
        test_buttons_layout.addWidget(self.test_email_btn)
        test_buttons_layout.addStretch()  # 保持按钮左对齐
        
        notification_layout.addWidget(test_buttons_widget, len(notification_config_items) + 2, 1)
        
        # 添加到主布局
        config_layout.addWidget(betting_group)
        config_layout.addWidget(notification_group)
        
        # 设置组框的最小宽度
        betting_group.setMinimumWidth(400)
        notification_group.setMinimumWidth(400)
        
        self.tabs.addTab(config_widget, "⚙️ 软件配置")
    
    def setup_log_area(self, parent):
        """设置日志区域"""
        log_group = QGroupBox("运行日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # PyQt6中使用document().setMaximumBlockCount()限制日志行数
        self.log_text.document().setMaximumBlockCount(1000)
        
        log_layout.addWidget(self.log_text)
        parent.addWidget(log_group)
    
    def setup_status_bar(self):
        """设置状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # 运行时间
        self.runtime_label = QLabel("运行时间: 0天0小时0分0秒")
        self.runtime_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        
        # 余额信息
        self.balance_label = QLabel("当前余额: 未登录")
        self.balance_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        
        self.pending_label = QLabel("未结算: 未登录")
        self.pending_label.setStyleSheet("font-weight: bold; color: #FF9800;")
        
        # 作者信息
        self.author_label = QLabel("Author: _0xAiCode | Enhanced Version")
        self.author_label.setStyleSheet("color: #757575;")
        
        # 添加到状态栏
        status_bar.addWidget(self.runtime_label)
        status_bar.addWidget(QLabel("|"))
        status_bar.addWidget(self.balance_label)
        status_bar.addWidget(QLabel("|"))
        status_bar.addWidget(self.pending_label)
        status_bar.addPermanentWidget(self.author_label)
    
    def setup_connections(self):
        """设置信号连接"""
        # 控件连接
        self.theme_combo.currentTextChanged.connect(self.apply_theme)
        self.login_btn.clicked.connect(self.member_login)
        self.start_btn.clicked.connect(self.toggle_monitoring)
        self.clear_btn.clicked.connect(self.clear_log)
        self.load_pending_btn.clicked.connect(self.load_pending_orders)
        self.load_history_btn.clicked.connect(self.load_history_orders)
        self.test_wechat_btn.clicked.connect(self.test_wechat_notification)
        self.test_email_btn.clicked.connect(self.test_email_notification)
        
        # 新增信号连接
        self.enable_profit_notification.toggled.connect(self.toggle_profit_interval_visibility)
    
    def toggle_profit_interval_visibility(self, checked):
        """控制推送间隔输入框的显示/隐藏"""
        self.profit_interval_widget.setVisible(checked)
        self.profit_interval_label.setVisible(checked)
    
    def setup_timers(self):
        """设置定时器"""
        # 运行时间更新定时器
        self.runtime_timer = QTimer()
        self.runtime_timer.timeout.connect(self.update_runtime)
        self.runtime_timer.start(1000)  # 每秒更新
    
    def apply_theme(self, theme_name):
        """应用主题"""
        if theme_name in THEMES:
            self.setStyleSheet(THEMES[theme_name])
            self.log(f"已切换到 {theme_name}")
    
    def log(self, message):
        # 自动重登录期间不输出任何日志，防止UI卡顿
        if getattr(self, '_relogin_active', False):
            return
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"【{timestamp}】>> {message}"
        self.log_text.append(formatted_message)
        self.logger.info(message)
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log("日志已清空")
    
    def update_runtime(self):
        """更新运行时间"""
        if self.start_time:
            elapsed = int(time.time() - self.start_time)
            days = elapsed // (24 * 3600)
            hours = (elapsed % (24 * 3600)) // 3600
            minutes = (elapsed % 3600) // 60
            seconds = elapsed % 60
            self.runtime_label.setText(f"运行时间: {days}天{hours}小时{minutes}分{seconds}秒")
    
    def member_login(self):
        domain = self.domain_input.currentText().strip()
        username = self.account_input.text().strip()
        password = self.password_input.text().strip()
        ver = self.ver_input.text().strip()
        if not all([domain, username, password, ver]):
            self.log("登录信息不完整，请检查输入")
            self.login_status_label.setText("登录失败")
            self.login_status_label.setStyleSheet("color: #F44336; font-weight: bold;")
            return
        self.log("正在登录...")
        self.login_btn.setEnabled(False)
        self.login_status_label.setText("登录中...")
        self.login_status_label.setStyleSheet("color: #FF9800; font-weight: bold;")
        # 启动登录线程
        mobile_mode = self.mobile_mode_checkbox.isChecked()
        self.login_thread = LoginThread(domain, username, password, ver, mobile_mode)
        self.login_thread.login_result.connect(self.on_login_result)
        self.login_thread.start()

    def on_login_result(self, success, user_info, error_msg):
        self.login_btn.setEnabled(True)
        if success:
            self.user_info = user_info
            self.log(f"登录成功！UID: {self.user_info['uid']}")
            self.login_status_label.setText("账号在线")
            self.login_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            self.save_config()
        else:
            self.login_status_label.setText("登录失败")
            self.login_status_label.setStyleSheet("color: #F44336; font-weight: bold;")
            self.log(f"登录失败: {error_msg}")
    
    def start_balance_check(self):
        """启动余额检查"""
        if self.balance_thread and self.balance_thread.isRunning():
            self.balance_thread.stop()
            # 不再wait，直接返回，避免阻塞主线程
            # self.balance_thread.wait()
        mobile_mode = self.mobile_mode_checkbox.isChecked()
        self.balance_thread = BalanceCheckThread(self.domain_input.currentText().strip(), self.user_info, mobile_mode)
        self.balance_thread.balance_updated.connect(self.update_balance_display)
        self.balance_thread.pending_orders_updated.connect(self.update_pending_orders_from_balance_check)
        self.balance_thread.login_required.connect(self.handle_login_required)
        self.balance_thread.log_message.connect(self.log)
        self.balance_thread.relogin_required.connect(self.handle_relogin_required)  # 新增
        self.balance_thread.start()
    
    def update_balance_display(self, balance, pending):
        """更新余额显示"""
        self.balance_label.setText(f"当前余额: {balance}")
        self.pending_label.setText(f"未结算: {pending}")
    
    def update_pending_orders_from_balance_check(self, orders_list, total_gold, order_count):
        """从余额检查线程更新未结订单表格"""
        try:
            # 清空当前表格
            self.pending_order_table.setRowCount(0)
            
            # 添加订单到表格
            for item in orders_list:
                self.add_pending_order_to_table(item)
            
            # 更新统计信息
            self.pending_stats_total.setText(f"下单总额: {total_gold}")
            self.pending_stats_count.setText(f"订单数: {order_count}")
            
            # 只在订单数量发生变化时记录日志，避免频繁输出
            if not hasattr(self, '_last_pending_count') or self._last_pending_count != order_count:
                self._last_pending_count = order_count
                # if order_count > 0:
                #     self.log(f"自动更新未结订单: {order_count} 条，总额: {total_gold}")
                    
        except Exception as e:
            self.log(f"更新未结订单表格失败: {str(e)}")
    
    def handle_login_required(self):
        """处理需要重新登录的情况"""
        # self.log("检测到登录状态异常，建议重新登录")
        # self.login_status_label.setText("需要重新登录")
        self.login_status_label.setStyleSheet("color: #FF5722; font-weight: bold;")
    
    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.is_monitoring:
            if not self.user_info.get("uid"):
                self.log("请先登录账号")
                return
            
            service_api = self.service_api_input.text().strip()
            if not service_api:
                self.log("请先配置服务数据API")
                return
            
            self.start_monitoring()
        else:
            self.stop_monitoring()
    
    def start_monitoring(self):
        """开始监控"""
        self.is_monitoring = True
        self.start_btn.setText("停止监控")
        self.start_btn.setStyleSheet("background-color: #F44336;")
        self.start_time = time.time()
        
        # 启动余额检查线程
        self.start_balance_check()
        
        # 启动订单监控线程
        service_api = self.service_api_input.text().strip()
        min_filter = self.enable_min_order_filter.isChecked()
        min_amount = float(self.min_order_amount_input.text() or "10")
        
        self.monitor_thread = OrderMonitorThread(service_api, self.processed_wids, min_filter, min_amount)
        self.monitor_thread.order_received.connect(self.handle_new_order)
        self.monitor_thread.log_message.connect(self.log)
        self.monitor_thread.start()
        
        # 启动盈利推送线程（如果启用）
        if self.enable_profit_notification.isChecked():
            self.start_profit_notification()
        
        self.log("开始监控订单...")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.start_btn.setText("开始监控")
        self.start_btn.setStyleSheet("")
        self.start_time = None
        
        # 停止余额检查线程
        if self.balance_thread and self.balance_thread.isRunning():
            self.balance_thread.stop()
        
        # 停止监控线程
        if self.monitor_thread and self.monitor_thread.isRunning():
            self.monitor_thread.stop()
            # 不再wait，避免阻塞主线程
            # self.monitor_thread.wait()
        # 停止所有下注线程
        for thread in self.betting_threads:
            if thread.isRunning():
                thread.terminate()  # 直接终止，避免wait阻塞
                # thread.wait()
        self.betting_threads.clear()
        
        # 停止盈利推送线程
        if self.profit_thread and self.profit_thread.isRunning():
            self.profit_thread.stop()
        
        # 保存已处理订单
        self.save_processed_wids()
        self.log("监控已停止")
    
    def start_profit_notification(self):
        """启动盈利推送线程"""
        try:
            # 停止现有线程
            if self.profit_thread and self.profit_thread.isRunning():
                self.profit_thread.stop()
            
            # 获取配置
            domain = self.domain_input.currentText().strip()
            mobile_mode = self.mobile_mode_checkbox.isChecked()
            interval_minutes = int(self.profit_interval_input.text() or "30")
            
            # 创建盈利推送线程
            self.profit_thread = ProfitNotificationThread(
                domain, self.user_info, mobile_mode, interval_minutes
            )
            
            # 连接信号
            self.profit_thread.profit_updated.connect(self.handle_profit_update)
            self.profit_thread.log_message.connect(self.log)
            
            # 启动线程
            self.profit_thread.start()
            
            self.log(f"盈利推送线程已启动，间隔: {interval_minutes} 分钟")
            
        except Exception as e:
            self.log(f"启动盈利推送线程失败: {str(e)}")
    
    def handle_profit_update(self, total_gold, total_vgold, total_winloss):
        """处理盈利更新"""
        try:
            # 构建推送消息
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message = f"【盈利推送】\n时间: {current_time}\n订单总额: {total_gold}\n总有效金额: {total_vgold}\n总输赢: {total_winloss}"
            
            # 发送微信通知
            if self.enable_wechat_notification.isChecked():
                self.send_wechat_notification("盈利推送", message)
            
            # 发送邮箱通知
            if self.enable_email_notification.isChecked():
                self.send_email_notification("盈利推送", message)
            
            self.log(f"盈利推送完成 - 订单总额: {total_gold}, 总有效金额: {total_vgold}, 总输赢: {total_winloss}")
            
        except Exception as e:
            self.log(f"处理盈利更新失败: {str(e)}")
    
    def send_wechat_notification(self, title, content):
        """发送微信通知"""
        try:
            wechat_uids = self.wechat_uid_input.text().strip()
            if not wechat_uids:
                return
            
            # 使用WxPusher推送消息
            url = "https://wxpusher.zjiecode.com/api/send/message"
            headers = {"Content-Type": "application/json"}
            app_token = "AT_yTDEgDCdt4olq1fY0CpZJipF5zhIPYCH"  # TODO: 替换为你的AppToken
            uids = [uid.strip() for uid in re.split(r'[，,;；\s]+', wechat_uids) if uid.strip()]
            
            if not uids:
                return
            
            data = {
                "appToken": app_token,
                "content": content,
                "summary": title or content[:20],
                "contentType": 2,
                "uids": uids,
                "url": "https://wxpusher.zjiecode.com",
                "verifyPayType": 0
            }
            
            resp = requests.post(url, headers=headers, data=json.dumps(data), timeout=10, verify=False)
            if resp.status_code == 200:
                self.log(f"微信推送成功: {title}")
            else:
                self.log(f"微信推送失败: {resp.text}")
                
        except Exception as e:
            self.log(f"微信推送异常: {str(e)}")
    
    def handle_new_order(self, order):
        """处理新订单"""
        row = self.order_table.rowCount()
        self.order_table.insertRow(row)
        
        # 添加订单数据到表格
        items = [
            order.get("MatchInfo", ""),
            order.get("MatchType", ""),
            order.get("PlayType", ""),
            order.get("Hdp", ""),
            order.get("OrderMoney", ""),
            order.get("Score", ""),
            "",  # 赔率稍后填充
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            order.get("status", "开始处理")
        ]
        
        for col, item_text in enumerate(items):
            item = QTableWidgetItem(str(item_text))
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.order_table.setItem(row, col, item)
        # 保存订单数据
        self.order_data[row] = order
        
        # 检查订单状态，只有正常订单才进行下注
        order_status = order.get("status", "开始处理")
        if order_status in ["已超时", "小额过滤"]:
            # 对于超时或小额过滤的订单，不进行下注
            self.log(f"订单 {order.get('Wid', '')} 状态为 {order_status}，跳过下注")
            return
        
        # 检查尾数过滤
        if self.enable_tail_filter.isChecked():
            try:
                tail_filter = int(self.tail_filter_input.text() or "0")
                order_amount = float(order.get("OrderMoney", 0))
                order_amount_tail = int(order_amount) % 10  # 获取金额的个位数
                
                if order_amount_tail == tail_filter:
                    self.log(f"订单 {order.get('Wid', '')} 金额 {order_amount} 尾数为 {order_amount_tail}，匹配过滤条件，跳过下注")
                    self.update_table_item(row, 8, "尾数过滤")
                    return
            except (ValueError, TypeError) as e:
                self.log(f"尾数过滤检查异常: {str(e)}")
        
        # 如果是正常订单且启用自动下注金额，则开始下注
        self.start_betting(order, row)
    
    def start_betting(self, order, row, custom_bet_amount=None):
        """开始下注"""
        try:
            # 计算下注金额
            if custom_bet_amount is not None:
                bet_amount = custom_bet_amount
            else:
                bet_amount_str = self.bet_amount_input.text() or "50"
                if self.fixed_amount_radio.isChecked():
                    bet_amount = str(int(float(bet_amount_str)))
                else:
                    # 百分比模式
                    balance = float(order.get("OrderMoney", 0))
                    percent = float(bet_amount_str) / 100
                    bet_amount = str(int(balance * percent))
            
            # 获取重试配置
            retry_count = int(self.retry_input.text() or "20")
            retry_delay = int(float(self.delay_input.text() or "3"))
            
            # 创建下注线程，传递自动调整金额的标志
            mobile_mode = self.mobile_mode_checkbox.isChecked()
            betting_thread = BettingThread(
                order, row, 
                self.domain_input.currentText().strip(),
                self.user_info,
                bet_amount,
                retry_count,
                retry_delay,
                auto_adjust_amount=self.auto_bet_amount_checkbox.isChecked(),
                mobile_mode=mobile_mode
            )
            
            betting_thread.betting_result.connect(self.handle_betting_result)
            betting_thread.log_message.connect(self.log)
            betting_thread.finished.connect(lambda: self.cleanup_betting_thread(betting_thread))
            
            self.betting_threads.append(betting_thread)
            betting_thread.start()
            
        except Exception as e:
            self.log(f"启动下注失败: {str(e)}")
            self.update_table_item(row, 8, "下注异常")
    
    def handle_betting_result(self, row, status, result_data):
        """处理下注结果"""
        self.update_table_item(row, 8, status)
        
        # 更新相关数据
        if "odds" in result_data:
            self.update_table_item(row, 6, result_data["odds"])
        
        if "score" in result_data:
            self.update_table_item(row, 5, result_data["score"])
        
        if "amount" in result_data:
            self.update_table_item(row, 4, str(result_data["amount"]))
    
    def update_table_item(self, row, col, value):
        """更新表格项"""
        if row < self.order_table.rowCount() and col < self.order_table.columnCount():
            item = QTableWidgetItem(str(value))
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 根据状态设置颜色
            if col == 8:  # 状态列
                if "成功" in str(value):
                    item.setForeground(QColor("#4CAF50"))
                elif "失败" in str(value):
                    item.setForeground(QColor("#F44336"))
                elif "中" in str(value):
                    item.setForeground(QColor("#FF9800"))
                elif "尾数过滤" in str(value):
                    item.setForeground(QColor("#9C27B0"))  # 紫色
                elif "小额过滤" in str(value):
                    item.setForeground(QColor("#FF5722"))  # 深橙色
            
            self.order_table.setItem(row, col, item)
    
    def cleanup_betting_thread(self, thread):
        """清理下注线程"""
        if thread in self.betting_threads:
            self.betting_threads.remove(thread)
    
    def show_order_context_menu(self, pos):
        """显示订单右键菜单"""
        row = self.order_table.rowAt(pos.y())
        if row == -1:
            return
        
        menu = QMenu(self)
        
        # 再次下单
        retry_action = QAction("🔄 再次下单", self)
        retry_action.triggered.connect(lambda: self.retry_bet(row))
        menu.addAction(retry_action)
        
        menu.addSeparator()
        
        # 清空列表
        clear_action = QAction("🗑️ 清空订单列表", self)
        clear_action.triggered.connect(self.clear_order_table)
        menu.addAction(clear_action)
        
        menu.exec(self.order_table.viewport().mapToGlobal(pos))
    
    def retry_bet(self, row):
        """重新下注"""
        order = self.order_data.get(row)
        if not order:
            self.log("无法获取订单数据")
            return
        
        bet_amount, ok = QInputDialog.getDouble(
            self, "再次下单", "请输入下单金额:",
            float(self.order_table.item(row, 4).text() or "50"),
            0, 1000000, 2
        )
        
        if ok:
            self.log(f"开始再次下单，金额: {int(bet_amount)}")
            self.start_betting(order, row, custom_bet_amount=str(int(bet_amount)))
    
    def clear_order_table(self):
        """清空订单表格"""
        reply = QMessageBox.question(
            self,
            "确认清空",
            "清空订单列表后无法手动重新下单，是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.order_table.setRowCount(0)
            self.order_data.clear()
            self.log("订单列表已清空")
    
    def load_pending_orders(self):
        """加载未结订单"""
        if not self.user_info.get("uid"):
            self.log("请先登录账号")
            return
        
        domain = self.domain_input.currentText().strip()
        if not domain:
            self.log("请先输入域名")
            return
        
        self.log("正在加载未结订单...")
        self.load_pending_btn.setEnabled(False)
        
        try:
            if not domain.startswith(("http://", "https://")):
                domain = f"https://{domain}"
            
            url = f"{domain}/transform.php?ver={self.user_info['ver']}"
            ts = str(int(datetime.now().timestamp() * 1000))
            
            data = (
                f"p=get_today_wagers&uid={self.user_info['uid']}&langx=zh-cn"
                f"&LS=g&selGtype=ALL&chk_cw=N&ts={ts}&format=json&db_slow=N"
            )
            
            # 根据手机版模式设置User-Agent
            mobile_mode = self.mobile_mode_checkbox.isChecked()
            if mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            
            response = make_request(url, data, headers, timeout=15, description="手动加载未结订单")

            data = response.json()
            self.pending_order_table.setRowCount(0)
            
            for item in data.get("wagers", []):
                if item.get("result"):
                    self.add_pending_order_to_table(item)
            
            total_gold = data.get("amout_gold", "0")
            order_count = data.get("count", 0)
            
            self.pending_stats_total.setText(f"下单总额: {total_gold}")
            self.pending_stats_count.setText(f"订单数: {order_count}")
            
            self.log(f"成功加载 {self.pending_order_table.rowCount()} 条未结订单")
        
        except Exception as e:
            self.log(f"加载未结订单失败: {str(e)}")
        
        finally:
            self.load_pending_btn.setEnabled(True)
    
    def add_pending_order_to_table(self, item):
        """添加未结订单到表格"""
        row = self.pending_order_table.rowCount()
        self.pending_order_table.insertRow(row)
        
        match_info = f"{item.get('league', '')} {item.get('team_h_show', '')} vs {item.get('team_c_show', '')}"
        
        items = [
            item.get("gtype", ""),
            item.get("w_id", ""),
            match_info,
            item.get("score", ""),
            item.get("result", ""),
            str(item.get("gold", "")),
            str(item.get("ioratio", "")),
            item.get("adddate", "")
        ]
        
        for col, item_text in enumerate(items):
            table_item = QTableWidgetItem(str(item_text))
            table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.pending_order_table.setItem(row, col, table_item)
    
    def load_history_orders(self):
        """加载历史订单"""
        if not self.user_info.get("uid"):
            self.log("请先登录账号")
            return
        
        domain = self.domain_input.currentText().strip()
        if not domain:
            self.log("请先输入域名")
            return
        
        self.log("正在加载历史订单...")
        self.load_history_btn.setEnabled(False)
        
        try:
            if not domain.startswith(("http://", "https://")):
                domain = f"https://{domain}"
            
            url = f"{domain}/transform.php?ver={self.user_info['ver']}"
            data = f"p=get_history_data&uid={self.user_info['uid']}&langx=zh-cn&gtype=ALL&isAll=N&startdate=&enddate=&filter=Y"
            
            # 根据手机版模式设置User-Agent
            mobile_mode = self.mobile_mode_checkbox.isChecked()
            if mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            
            response = make_request(url, data, headers, timeout=15, description="加载历史订单")
            root = ET.fromstring(response.text)

            code = root.find("code").text
            if code != "607":
                error_msg = get_error_message(code)
                raise ValueError(f"历史订单加载失败 {error_msg}")
            
            self.order_history_table.setRowCount(0)
            
            for history in root.findall("history"):
                date = history.find("date").text
                date_name = history.find("date_name").text
                gold = history.find("gold").text
                vgold = history.find("vgold").text
                winloss = history.find("winloss").text
                
                self.add_history_order_to_table(date, date_name, gold, vgold, winloss)
            
            # 更新统计信息
            total_gold = root.find("total_gold").text
            total_vgold = root.find("total_vgold").text
            total_winloss = root.find("total_winloss").text
            
            self.history_stats_total_gold.setText(f"订单总额: {total_gold}")
            self.history_stats_total_vgold.setText(f"总有效金额: {total_vgold}")
            self.history_stats_total_winloss.setText(f"总输赢: {total_winloss}")
            
            self.log(f"成功加载 {self.order_history_table.rowCount()} 条历史订单")
        
        except Exception as e:
            self.log(f"加载历史订单失败: {str(e)}")
        
        finally:
            self.load_history_btn.setEnabled(True)
    
    def add_history_order_to_table(self, date, date_name, gold, vgold, winloss):
        """添加历史订单到表格"""
        row = self.order_history_table.rowCount()
        self.order_history_table.insertRow(row)
        
        items = [date_name, gold, vgold, winloss]
        
        for col, item_text in enumerate(items):
            table_item = QTableWidgetItem(str(item_text))
            table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 设置输赢颜色
            if col == 3 and item_text != "-":
                try:
                    value = float(item_text)
                    if value > 0:
                        table_item.setForeground(QColor("#4CAF50"))
                    elif value < 0:
                        table_item.setForeground(QColor("#F44336"))
                except ValueError:
                    pass
            
            self.order_history_table.setItem(row, col, table_item)
        
        # 添加查看明细按钮
        if gold != "-" and vgold != "-" and winloss != "-":
            btn = QPushButton("查看明细")
            btn.setFixedSize(70, 25)
            btn.clicked.connect(lambda: self.load_history_details(date))
            self.order_history_table.setCellWidget(row, 4, btn)
    
    def load_history_details(self, date):
        """加载历史明细"""
        if not self.user_info.get("uid"):
            self.log("请先登录账号")
            return
        
        domain = self.domain_input.currentText().strip()
        if not domain:
            self.log("请先输入域名")
            return
        
        self.log(f"正在加载日期 {date} 的历史明细...")
        
        try:
            if not domain.startswith(("http://", "https://")):
                domain = f"https://{domain}"
            
            url = f"{domain}/transform.php?ver={self.user_info['ver']}"
            data = (
                f"p=history_switch"
                f"&uid={self.user_info['uid']}"
                f"&langx=zh-cn"
                f"&LS=c"
                f"&today_gmt={date}"
                f"&gtype=ALL&tmp_flag=Y"
            )
            
            # 根据手机版模式设置User-Agent
            mobile_mode = self.mobile_mode_checkbox.isChecked()
            if mobile_mode:
                user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": user_agent
            }
            
            response = make_request(url, data, headers, timeout=15, description="加载历史明细")
            
            root = ET.fromstring(response.text)
            code = root.find("code").text
            if code != "609":
                error_msg = get_error_message(code)
                raise ValueError(f"历史明细加载失败 {error_msg}")
            
            self.history_details_table.setRowCount(0)
            
            for wager in root.findall("wagers"):
                self.add_history_detail_to_table(wager)
            
            self.log(f"成功加载日期 {date} 的 {self.history_details_table.rowCount()} 条历史明细")
            
            # 切换到历史明细标签页
            for i in range(self.tabs.count()):
                if self.tabs.tabText(i) == "📋 历史明细":
                    self.tabs.setCurrentIndex(i)
                    break
        
        except Exception as e:
            self.log(f"加载历史明细失败: {str(e)}")
    
    def add_history_detail_to_table(self, wager):
        """添加历史明细到表格"""
        row = self.history_details_table.rowCount()
        self.history_details_table.insertRow(row)
        
        def get_text(element, default=""):
            found = wager.find(element)
            return found.text if found is not None else default
        
        match_info = f"{get_text('league')} {get_text('team_h_show')} vs {get_text('team_c_show')}"
        win_gold_str = get_text('win_gold', '0')
        
        try:
            win_gold = float(win_gold_str)
        except ValueError:
            # self.log(f"无效的 win_gold 值: {win_gold_str}, 记录 w_id: {get_text('w_id')}")
            win_gold = 0
            win_gold_str = "-"
        
        items = [
            get_text("gtype"),
            get_text("w_id"),
            match_info,
            get_text("result"),
            get_text("ioratio"),
            get_text("gold"),
            get_text("result_data"),
            get_text("ball_act_ret"),
            win_gold_str
        ]
        
        for col, item_text in enumerate(items):
            table_item = QTableWidgetItem(str(item_text))
            table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 设置输赢颜色
            if col == 8 and win_gold_str:  # 输赢列
                if win_gold > 0:
                    table_item.setForeground(QColor("#4CAF50"))  # 绿色表示赢
                elif win_gold < 0:
                    table_item.setForeground(QColor("#F44336"))  # 红色表示输
            
            self.history_details_table.setItem(row, col, table_item)
    
    def edit_wechat_uids(self):
        """编辑微信UID列表"""
        current_text = self.wechat_uid_input.text()
        dialog = MultiLineEditDialog(
            self, 
            "编辑微信通知", 
            current_text, 
            "每行一个微信UID"
        )
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_text = dialog.get_text()
            self.wechat_uid_input.setText(new_text)
            self.log(f"微信通知列表已更新: {len(new_text.split('、')) if new_text else 0} 个")
    
    def edit_email_addresses(self):
        """编辑邮箱地址列表"""
        current_text = self.email_address_input.text()
        dialog = MultiLineEditDialog(
            self, 
            "编辑收件地址", 
            current_text, 
            "每行一个邮箱地址"
        )
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_text = dialog.get_text()
            self.email_address_input.setText(new_text)
            self.log(f"收件地址列表已更新: {len(new_text.split('、')) if new_text else 0} 个")
    
    def test_wechat_notification(self):
        """测试微信通知"""
        if not self.enable_wechat_notification.isChecked():
            self.log("微信通知未启用")
            return
        
        wechat_uids = self.wechat_uid_input.text().strip()
        if not wechat_uids:
            self.log("请先配置微信通知列表")
            return
        
        uid_list = [uid.strip() for uid in re.split(r'[，,;；\s]+', wechat_uids) if uid.strip()]
        if not uid_list:
            self.log("微信UID格式错误，请检查配置")
            return
        
        self.log(f"正在测试微信通知...（共 {len(uid_list)} 个UID）")
        
        try:
            # 发送测试消息
            test_content = f"这是一条来自HG投注客户端V2.0的测试消息。\n\n如果您收到此消息，说明微信通知配置成功！\n\n发送时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            self.send_wechat_notification("微信通知测试", test_content)
            self.log("微信通知测试完成")
            
        except Exception as e:
            self.log(f"微信通知测试异常: {str(e)}")
    
    def test_email_notification(self):
        """测试邮箱通知"""
        if not self.enable_email_notification.isChecked():
            self.log("邮箱通知未启用")
            return
        
        # 检查邮箱配置
        email_addresses = self.email_address_input.text().strip()
        smtp_server = self.smtp_server_input.text().strip()
        smtp_port = self.smtp_port_input.text().strip()
        sender_email = self.sender_email_input.text().strip()
        email_password = self.email_password_input.text().strip()
        
        if not all([email_addresses, smtp_server, smtp_port, sender_email, email_password]):
            self.log("邮箱配置不完整，请检查所有邮箱设置")
            return
        
        email_list = [email.strip() for email in email_addresses.split('、') if email.strip()]
        self.log(f"正在测试邮箱通知...（共 {len(email_list)} 个邮箱）")
        
        try:
            # 发送测试邮件
            success_count = self.send_email_notification(
                "HG投注客户端测试通知",
                "这是一封来自HG投注客户端V2.0的测试邮件。\n\n如果您收到此邮件，说明邮箱通知配置成功！\n\n发送时间：" + 
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
            
            if success_count > 0:
                self.log(f"邮箱通知测试完成，成功发送 {success_count}/{len(email_list)} 封邮件")
            else:
                self.log("邮箱通知测试失败，请检查配置")
                
        except Exception as e:
            self.log(f"邮箱通知测试异常: {str(e)}")
    
    def send_email_notification(self, subject, content):
        """发送邮箱通知"""
        try:
            if not self.enable_email_notification.isChecked():
                return 0
            
            # 获取邮箱配置
            email_addresses = self.email_address_input.text().strip()
            smtp_server = self.smtp_server_input.text().strip()
            smtp_port = int(self.smtp_port_input.text().strip())
            sender_email = self.sender_email_input.text().strip()
            email_password = self.email_password_input.text().strip()
            
            if not all([email_addresses, smtp_server, sender_email, email_password]):
                return 0
            
            # 解析邮箱地址列表
            email_list = [email.strip() for email in email_addresses.split('、') if email.strip()]
            if not email_list:
                return 0
            
            success_count = 0
            
            # 建立SMTP连接
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()  # 启用TLS加密
            server.login(sender_email, email_password)
            
            # 逐个发送邮件
            for email_address in email_list:
                try:
                    # 创建邮件
                    msg = MIMEMultipart()
                    msg['From'] = sender_email
                    msg['To'] = email_address
                    msg['Subject'] = subject
                    
                    # 添加邮件内容
                    msg.attach(MIMEText(content, 'plain', 'utf-8'))
                    
                    # 发送邮件
                    text = msg.as_string()
                    server.sendmail(sender_email, email_address, text)
                    success_count += 1
                    
                except Exception as e:
                    self.log(f"发送邮件到 {email_address} 失败: {str(e)}")
            
            server.quit()
            return success_count
            
        except Exception as e:
            self.log(f"邮箱通知发送失败: {str(e)}")
            return 0
    
    def save_processed_wids(self):
        """保存已处理订单"""
        try:
            with open("order.json", "w", encoding="utf-8") as f:
                json.dump(list(self.processed_wids), f, ensure_ascii=False, indent=2)
            self.log(f"已保存 {len(self.processed_wids)} 个已处理订单")
        except Exception as e:
            self.log(f"保存已处理订单失败: {str(e)}")
    
    def load_processed_wids(self):
        """加载已处理订单"""
        try:
            if os.path.exists("order.json"):
                with open("order.json", "r", encoding="utf-8") as f:
                    wids = json.load(f)
                    self.processed_wids.update(wids)
                    self.log(f"已加载 {len(wids)} 个已处理订单")
            else:
                self.log("order.json 不存在，使用空列表")
        except Exception as e:
            self.log(f"加载已处理订单失败: {str(e)}")
    
    def save_config(self):
        """保存配置"""
        try:
            config = configparser.ConfigParser()
            
            config['Connection'] = {
                'Theme': self.theme_combo.currentText(),
                'Account': self.account_input.text(),
                'Password': self.password_input.text(),
                'Ver': self.ver_input.text(),
                'Domain': self.domain_input.currentText(),
                'ReverseBetting': str(self.reverse_betting_checkbox.isChecked()),  # 添加反向投注配置
                'MobileMode': str(self.mobile_mode_checkbox.isChecked())  # 添加手机版配置
            }
            
            config['Betting'] = {
                'RetryCount': self.retry_input.text(),
                'RetryDelay': self.delay_input.text(),
                'LoginCheck': self.login_check_input.text(),
                'BetAmount': self.bet_amount_input.text(),
                'AmountMode': 'fixed' if self.fixed_amount_radio.isChecked() else 'percent',
                'ServiceApi': self.service_api_input.text(),
                'AutoBetAmount': str(self.auto_bet_amount_checkbox.isChecked()),
                'MinOrderAmount': self.min_order_amount_input.text(),
                'EnableMinOrderFilter': str(self.enable_min_order_filter.isChecked()),
                'TailFilter': self.tail_filter_input.text(),
                'EnableTailFilter': str(self.enable_tail_filter.isChecked())
            }
            
            config['Notification'] = {
                'WechatUID': self.wechat_uid_input.text(),
                'EmailAddress': self.email_address_input.text(),
                'SMTPServer': self.smtp_server_input.text(),
                'SMTPPort': self.smtp_port_input.text(),
                'SenderEmail': self.sender_email_input.text(),
                'EmailPassword': self.email_password_input.text(),
                'EnableWechatNotification': str(self.enable_wechat_notification.isChecked()),
                'EnableEmailNotification': str(self.enable_email_notification.isChecked()),
                'EnableProfitNotification': str(self.enable_profit_notification.isChecked()),
                'ProfitInterval': self.profit_interval_input.text()
            }
            
            with open('ClientConfig.ini', 'w', encoding='utf-8') as configfile:
                config.write(configfile)
            
            self.log("配置已保存")
        except Exception as e:
            self.log(f"保存配置失败: {str(e)}")
    
    def load_config(self):
        """加载配置"""
        try:
            config = configparser.ConfigParser()
            if os.path.exists('ClientConfig.ini'):
                config.read('ClientConfig.ini', encoding='utf-8')
                
                # 连接配置
                if 'Connection' in config:
                    conn = config['Connection']
                    self.account_input.setText(conn.get('Account', ''))
                    self.password_input.setText(conn.get('Password', ''))
                    self.ver_input.setText(conn.get('Ver', ''))
                    self.domain_input.setCurrentText(conn.get('Domain', ''))
                    
                    # 加载反向投注配置（添加安全检查）
                    if hasattr(self, 'reverse_betting_checkbox'):
                        reverse_betting = conn.get('ReverseBetting', 'False') == 'True'
                        self.reverse_betting_checkbox.setChecked(reverse_betting)
                    
                    # 加载手机版配置
                    if hasattr(self, 'mobile_mode_checkbox'):
                        mobile_mode = conn.get('MobileMode', 'False') == 'True'
                        self.mobile_mode_checkbox.setChecked(mobile_mode)
                    
                    theme = conn.get('Theme', '默认主题')
                    self.theme_combo.setCurrentText(theme)
                    self.apply_theme(theme)
                
                # 下注配置
                if 'Betting' in config:
                    betting = config['Betting']
                    self.retry_input.setText(betting.get('RetryCount', '20'))
                    self.delay_input.setText(betting.get('RetryDelay', '3'))
                    self.login_check_input.setText(betting.get('LoginCheck', '30'))
                    self.bet_amount_input.setText(betting.get('BetAmount', '50'))
                    self.service_api_input.setText(betting.get('ServiceApi', ''))
                    self.min_order_amount_input.setText(betting.get('MinOrderAmount', '10'))
                    
                    # 设置单选按钮状态
                    amount_mode = betting.get('AmountMode', 'fixed')
                    if amount_mode == 'fixed':
                        self.fixed_amount_radio.setChecked(True)
                    else:
                        self.percent_amount_radio.setChecked(True)
                    
                    auto_bet_amount = betting.get('AutoBetAmount', 'True') == 'True'
                    self.auto_bet_amount_checkbox.setChecked(auto_bet_amount)
                    
                    enable_filter = betting.get('EnableMinOrderFilter', 'False') == 'True'
                    self.enable_min_order_filter.setChecked(enable_filter)
                    
                    # 加载尾数过滤配置
                    tail_filter = betting.get('TailFilter', '0')
                    self.tail_filter_input.setText(tail_filter)
                    
                    enable_tail_filter = betting.get('EnableTailFilter', 'False') == 'True'
                    self.enable_tail_filter.setChecked(enable_tail_filter)
                
                # 通知配置
                if 'Notification' in config:
                    notification = config['Notification']
                    self.wechat_uid_input.setText(notification.get('WechatUID', ''))
                    self.email_address_input.setText(notification.get('EmailAddress', ''))
                    self.smtp_server_input.setText(notification.get('SMTPServer', 'smtp.qq.com'))
                    self.smtp_port_input.setText(notification.get('SMTPPort', '587'))
                    self.sender_email_input.setText(notification.get('SenderEmail', ''))
                    self.email_password_input.setText(notification.get('EmailPassword', ''))
                    
                    enable_wechat = notification.get('EnableWechatNotification', 'False') == 'True'
                    self.enable_wechat_notification.setChecked(enable_wechat)
                    
                    enable_email = notification.get('EnableEmailNotification', 'False') == 'True'
                    self.enable_email_notification.setChecked(enable_email)
                    
                    enable_profit = notification.get('EnableProfitNotification', 'False') == 'True'
                    self.enable_profit_notification.setChecked(enable_profit)
                    
                    profit_interval = notification.get('ProfitInterval', '30')
                    self.profit_interval_input.setText(profit_interval)
                
                self.log("配置加载完成")
            else:
                self.log("配置文件不存在，使用默认配置")
        except Exception as e:
            self.log(f"加载配置失败: {str(e)}")
    
    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出软件吗？退出前将保存当前配置。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        if reply == QMessageBox.StandardButton.Yes:
            self.log("用户确认退出，正在保存配置...")
            # 停止监控
            if self.is_monitoring:
                self.stop_monitoring()
            # 停止余额检查线程
            if self.balance_thread and self.balance_thread.isRunning():
                self.balance_thread.stop()
                # 不再wait，避免阻塞主线程
                # self.balance_thread.wait()
            
            # 停止盈利推送线程
            if self.profit_thread and self.profit_thread.isRunning():
                self.profit_thread.stop()
            
            # 保存配置和数据
            self.save_config()
            self.save_processed_wids()
            self.log("程序退出")
            event.accept()
        else:
            event.ignore()

    def handle_relogin_required(self):
        domain = self.domain_input.currentText().strip()
        username = self.account_input.text().strip()
        password = self.password_input.text().strip()
        ver = self.ver_input.text().strip()
        mobile_mode = self.mobile_mode_checkbox.isChecked()
        self._relogin_params = (domain, username, password, ver, mobile_mode)
        self._relogin_active = True
        self._start_relogin_attempt()

    def _start_relogin_attempt(self):
        if not getattr(self, '_relogin_active', False):
            return
        domain, username, password, ver, mobile_mode = self._relogin_params
        self.relogin_thread = ReloginThread(domain, username, password, ver, mobile_mode)
        from PyQt6.QtCore import Qt
        self.relogin_thread.relogin_completed.connect(self.on_relogin_result, Qt.ConnectionType.QueuedConnection)
        self.relogin_thread.start()

    def on_relogin_result(self, success, user_info):
        if success:
            self._relogin_active = False
            self.user_info = user_info
            self.login_status_label.setText("账号在线")
            self.login_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            # 只有在监控状态下才启动余额检查
            if self.is_monitoring:
                self.start_balance_check()
            self.save_config()
        else:
            self.login_status_label.setText("自动重新登录失败，3秒后重试…")
            self.login_status_label.setStyleSheet("color: #F44336; font-weight: bold;")
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(3000, self._start_relogin_attempt)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("HG-下单客户端")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("_0xAiCode")
    
    # 设置应用图标（如果有的话）
    app.setWindowIcon(QIcon("icon.ico"))
    
    window = HGBettingClient()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()

