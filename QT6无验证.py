import sys
import json
import os
import re
import time
import subprocess
import configparser
import logging
from datetime import datetime
import requests
from requests.exceptions import SSLError
import urllib3
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit, QLabel,
    QTextEdit, QTabWidget, QHeaderView, QCheckBox, QComboBox,
    QDialog, QDialogButtonBox, QFormLayout, QMessageBox, QGroupBox
)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt6.QtGui import QFont, QBrush, QColor

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 订单爬取线程，使用 QThread 并发执行请求
class OrderFetcher(QThread):
    data_received = pyqtSignal(list)
    log_signal = pyqtSignal(str)
    update_account_signal = pyqtSignal(dict)
    update_monitor_message = pyqtSignal(str, str)  # 新增：更新监控消息信号 (account, message)
    request_relogin = pyqtSignal(dict, dict)  # 新增：请求重新登录信号 (response_data, account_info)

    def __init__(self, parent, domain, admin_info, login_type, delay=1.0, domains=None, account_info=None, monitor_member_id=None, global_ver=None):
        super().__init__(parent)
        self.domain = domain.rstrip('/')
        self.admin_info = admin_info
        self.login_type = login_type
        self.is_running = True
        self.session = requests.Session()
        self.sel_maxid = 0  # 原 CONST_ADMIN_Sel_MaxID
        self.delay = delay  # 使用传入的固定延迟值
        self.domains = domains or []
        self.account_info = account_info or {}
        self.monitor_member_id = monitor_member_id  # 新增：监控会员ID
        self.global_ver = global_ver
        self.login_retry_count = 0  # 登录重试计数器
        self.max_login_retries = 3  # 最大重试次数
        self.is_relogin_in_progress = False  # 是否正在重新登录中





    def run(self):
        # # 打印monitor_member_id的值用于调试
        # print(f"[调试] OrderFetcher启动 - 账号: {self.account_info.get('account', '未知')}")
        # print(f"[调试] monitor_member_id类型: {type(self.monitor_member_id)}")
        # print(f"[调试] monitor_member_id值: {self.monitor_member_id}")
        # if isinstance(self.monitor_member_id, list):
        #     print(f"[调试] monitor_member_id列表长度: {len(self.monitor_member_id)}")
        #     for i, member in enumerate(self.monitor_member_id):
        #         print(f"[调试] 监控会员[{i}]: {member}")
        
        while self.is_running:
            try:
                # 检查是否正在重新登录中，如果是则跳过本次循环
                if self.is_relogin_in_progress:
                    # 仍然需要等待固定延迟
                    self.interruptible_sleep(self.delay)
                    continue
                
                if not self.admin_info.get("uid") or not self.admin_info.get('ver'):
                    self.log_signal.emit("请先完成代理登录")
                    # 使用固定延迟，与正常监控保持一致
                    self.interruptible_sleep(self.delay)
                    continue
                ver = self.global_ver
                url = f"{self.domain}/transform.php?ver={ver}"
                login_layer = "ag" if self.login_type == "登3" else "su"
                payload = {
                    'login_layer': login_layer,
                    'uid': self.admin_info['uid'],
                    'langx': 'zh-cn',
                    'ver': self.admin_info['ver'],
                    'p': 'get_wmc_list_bet',
                    'totalBets': 'wmc',
                    'gtype': 'FT',
                    'sel_maxid': self.sel_maxid
                }
                
                # 使用session获取cookies
                session = self.admin_info.get('session')
                if session:
                    cookies_str = "; ".join([f"{k}={v}" for k, v in session.cookies.get_dict().items()])
                else:
                    cookies_str = self.admin_info.get("set_cookie", "")
                    if not cookies_str:
                        cookies_str = "; ".join([f"{k}={v}" for k, v in self.admin_info.get("cookies", {}).items()])
                
                # 使用主窗口的get_headers方法构建请求头
                headers = self.parent().get_headers(self.domain, cookies=cookies_str)
                
                # 使用主窗口的make_request方法发送请求
                response = self.parent().make_request(
                    session=session,
                    method="POST",
                    url=url,
                    data=payload,
                    headers=headers,
                    timeout=5,
                    description=f"获取账号 {self.account_info.get('account')} 的订单数据"
                )
                if not response:
                    # 显示到监控消息，不输出到日志
                    self.update_monitor_message.emit(self.account_info.get("account", ""), "网络请求失败")
                    continue
                
                json_data = response.json()
                logging.info(f"获取订单响应JSON: {json_data}")
                
                # 优先检查会话过期错误码（4X014/4X042）
                if json_data.get("code", "") == "4X014" or json_data.get("code", "")=="4X042":
                    if not self.is_running:
                        break
                    
                    # 设置重新登录状态，防止重复请求
                    if self.is_relogin_in_progress:
                        # 已经在重新登录中，跳过本次处理
                        continue
                    
                    self.is_relogin_in_progress = True
                    self.login_retry_count += 1
                    
                    # 发送重新登录请求信号，不等待结果
                    self.request_relogin.emit(json_data, self.account_info)
                    
                    # 直接跳过本次循环，等待下次循环时admin_info可能已经更新
                    continue
                
                # 检查其他status为error的情况，显示msg到监控消息
                if json_data.get("status") == "error":
                    error_msg = json_data.get("msg", "未知错误")
                    self.update_monitor_message.emit(self.account_info.get("account", ""), error_msg)
                    
                    # 检查无权限情况
                    if error_msg == "goToHome":
                        self.log_signal.emit("账号无权限，已停止监控")
                        # 通知主窗口更新账号状态
                        update_info = {
                            "account": self.account_info.get("account"),
                            "admin_info": self.admin_info,
                            "status": "账号无权限"
                        }
                        self.update_account_signal.emit(update_info)
                        self.is_running = False
                        break
                    continue
                # 正常获取到数据，重置登录重试计数器
                self.login_retry_count = 0
                
                maxid = json_data.get("maxid", self.sel_maxid)
                if maxid != self.sel_maxid:
                    self.sel_maxid = maxid
                wagers = json_data.get("wagers", [])
                if wagers:
                    # 处理所有订单，但标记哪些是指定会员的订单
                    processed = self.process_wagers(wagers)
                    if processed:
                        self.data_received.emit(processed)
                else:
                    # wagers为空时，显示监控编号
                    monitor_msg = f"监控编号: {maxid}"
                    self.update_monitor_message.emit(self.account_info.get("account", ""), monitor_msg)
            except Exception as e:
                self.log_signal.emit(f"账号 {self.account_info.get('account')} 获取下单数据失败: {str(e)}")
            
            # 可中断的延迟，提高停止响应速度
            self.interruptible_sleep(self.delay)

    def interruptible_sleep(self, delay_time):
        """可中断的延迟，每0.1秒检查一次是否需要停止"""
        sleep_count = int(delay_time * 10)  # 转换为0.1秒的倍数
        for _ in range(sleep_count):
            if not self.is_running:
                break
            time.sleep(0.1)
        
        # 处理剩余的小数部分
        remaining = delay_time - (sleep_count * 0.1)
        if remaining > 0 and self.is_running:
            time.sleep(remaining)

    def stop(self):
        self.is_running = False
        try:
            self.session.close()
        except Exception:
            pass

    def process_wagers(self, wagers):
        wtype_map = {
            "大/小-上半场": "HOU", "让球-上半场": "HR", "大/小": "OU", "让球": "R",
            "(滚球)让球": "RE", "(滚球)大/小": "ROU", "(滚球)让球-上半场": "HRE",
            "(滚球)大/小-上半场": "HROU", "(滚球)角球数-大/小-上半场": "HROU",
            "(滚球)角球数-大/小": "ROU", "(滚球)角球数-让球-上半场": "HRE",
            "(滚球)角球数-让球": "RE",
        }
        new_data = []
        for wager in wagers:
            wagerstype = wager.get("WAGERSTYPE", "").replace(" ", "")
            wtype = wtype_map.get(wagerstype, "Unknown")
            if wtype == "Unknown":
                continue
            order_type = wager.get("ORDER_TYPE", "").replace(" ", "").strip()
            team_h = "".join(wager.get("TEAM_H", "").split())
            team_c = "".join(wager.get("TEAM_C", "").split())
            chose_team = ""
            if "大/小" in wagerstype:
                chose_team = "H" if order_type == "小" else "C"
            elif "让球" in wagerstype:
                chose_team = "H" if order_type == team_h else "C"
            match_name = re.split(r'<br>', wager.get('TNAME', ''))[0]
            score = self.get_score(wager.get('TNAME', ""))
            
            # 检查是否是指定会员的订单（使用NAME0字段与username比较）
            monitor_usernames = []
            if isinstance(self.monitor_member_id, list):
                # 新格式：monitor_members列表，提取username
                monitor_usernames = [str(m.get('username', '')) for m in self.monitor_member_id if m.get('username')]
            else:
                # 兼容旧格式：如果是字符串，可能需要特殊处理
                # 这里假设旧格式已经不使用，如果需要可以添加兼容逻辑
                monitor_usernames = []
            
            # 使用NAME0字段进行比较（重要：NAME0是订单中的真实用户名）
            wager_username = wager.get("NAME0", "").strip()
            is_monitored = (monitor_usernames and wager_username in monitor_usernames)
            
            # 调试信息：记录比较过程
            if monitor_usernames:
                logging.info(f"订单用户名: '{wager_username}', 监控用户名列表: {monitor_usernames}, 是否匹配: {is_monitored}")
            
            data = {
                "Wid": wager.get("TID", ""),
                "MatchInfo": f"{match_name} - {team_h}-{team_c}",
                "MatchType": wagerstype,
                "PlayType": order_type,
                "Hdp": wager.get("ORDER_CON", ""),
                "OrderMoney": wager.get("GOLD", ""),
                "Score": score,
                "MemberID": wager.get("DOWNLINE", ""),
                "UserName": wager.get("NAME0", ""),
                "ts": time.time(),
                "is_monitored": is_monitored,  # 新增：标记是否是指定会员的订单
                "account": self.account_info.get('account', ''),  # 新增：代理账号信息
                "AddData": {
                    "p": "FT_order_view",
                    "uid": "",
                    "ver": "",
                    "langx": "zh-cn",
                    "odd_f_type": "H",
                    "gid": wager.get("_EVENT", ""),
                    "gtype": "FT",
                    "wtype": wtype,
                    "chose_team": chose_team
                },
                "CONST_ADMIN_Sel_MaxID": self.sel_maxid
            }
            new_data.append(data)
        return new_data

    def get_score(self, html_str):
        match = re.search(r'\(\d+\s*-\s*\d+\)', html_str)
        return match.group(0).strip('()').replace(' ', '') if match else "未开赛"



class AddAccountDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加账号")
        self.setFixedWidth(350)
        self.admin_info = None  # 存储登录信息
        layout = QFormLayout(self)
        # 域名 - 使用ComboBox支持选择和输入
        self.domain_input = QComboBox()
        self.domain_input.setEditable(True)  # 允许编辑输入
        self.domain_input.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)  # 不自动插入新项
        # 从主窗口获取已载入的域名列表
        if self.parent() and hasattr(self.parent(), 'domain_list') and self.parent().domain_list:
            for domain_info in self.parent().domain_list:
                domain = domain_info.get('domain', '')
                if domain:
                    self.domain_input.addItem(domain)
        self.domain_input.currentTextChanged.connect(self.clear_login_info)
        self.login_type_combo = QComboBox()
        self.login_type_combo.addItems(["登3", "登2"])
        self.login_type_combo.currentTextChanged.connect(self.toggle_security_code_input)
        self.account_input = QLineEdit()
        self.account_input.textChanged.connect(self.clear_login_info)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.textChanged.connect(self.clear_login_info)
        self.monitor_input = QLineEdit()
        self.security_code_label = QLabel("安全码：")
        self.security_code_input = QLineEdit()
        self.security_code_label.setVisible(False)
        self.security_code_input.setVisible(False)
        layout.addRow("登录类型：", self.login_type_combo)
        layout.addRow("域名：", self.domain_input)
        layout.addRow("账号：", self.account_input)
        layout.addRow("密码：", self.password_input)
        layout.addRow(self.security_code_label, self.security_code_input)
        layout.addRow("监控会员ID：", self.monitor_input)
        # 新增按钮行
        btn_row = QHBoxLayout()
        self.login_btn = QPushButton("登录账号")
        self.login_btn.clicked.connect(self.test_login)
        self.select_member_btn = QPushButton("选择会员")
        self.select_member_btn.clicked.connect(self.select_member)
        btn_row.addWidget(self.login_btn)
        btn_row.addWidget(self.select_member_btn)
        layout.addRow(btn_row)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addRow(btn_box)
    def clear_login_info(self):
        """当账号信息发生变化时清空登录信息"""
        self.admin_info = None
    def toggle_security_code_input(self, login_type):
        is_登2 = (login_type == "登2")
        self.security_code_label.setVisible(is_登2)
        self.security_code_input.setVisible(is_登2)
    def get_data(self):
        return {
            "login_type": self.login_type_combo.currentText(),
            "domain": self.domain_input.currentText().strip(),
            "account": self.account_input.text().strip(),
            "password": self.password_input.text().strip(),
            "security_code": self.security_code_input.text().strip() if self.login_type_combo.currentText() == "登2" else "",
            "monitor": self.monitor_input.text().strip(),
            "status": "未登录"
        }
    def test_login(self):
        # 调用主窗口的admin_login
        if self.parent() and hasattr(self.parent(), 'admin_login'):
            account_info = self.get_data()
            result = self.parent().admin_login(account_info)
            if result:
                self.admin_info = result  # 保存登录信息
                QMessageBox.information(self, "登录成功", "账号登录成功！可以选择会员了。")
            else:
                self.admin_info = None
                QMessageBox.warning(self, "登录失败", "账号登录失败，请检查信息或查看日志。")
        else:
            QMessageBox.warning(self, "错误", "未找到主窗口登录方法。")
    def select_member(self):
        # 使用新的监控会员选择弹窗
        if not self.admin_info:
            QMessageBox.warning(self, "错误", "请先点击'登录账号'按钮进行登录")
            return
        
        # MonitorMemberDialog会自动处理Ver获取，这里不需要预先获取
        
        # 构造临时账号数据用于MonitorMemberDialog
        account_info = self.get_data()
        temp_account_data = {
            'domain': account_info['domain'],
            'admin_info': self.admin_info,
            'login_type': account_info['login_type'],
            'monitor_members': []  # 新账号，初始为空
        }
        
        # 如果monitor_input中已有内容，解析为monitor_members
        current_monitor = self.monitor_input.text().strip()
        if current_monitor:
            # 尝试解析现有的监控会员信息（可能是ID列表或JSON格式）
            try:
                import json
                # 尝试JSON格式
                temp_account_data['monitor_members'] = json.loads(current_monitor)
            except:
                # 如果不是JSON，假设是逗号分隔的ID列表
                member_ids = [mid.strip() for mid in current_monitor.split(',') if mid.strip()]
                # 注意：这里应该保存真实的用户名，但在这个上下文中我们只有ID
                # 建议使用MonitorMemberDialog来正确选择和保存会员信息
                temp_account_data['monitor_members'] = [{'id': mid, 'username': f'会员{mid}'} for mid in member_ids]
        
        # 打开监控会员选择弹窗
        dialog = MonitorMemberDialog(self, temp_account_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            selected_members = dialog.get_monitor_members()
            # 将选择的会员保存为JSON格式到monitor_input
            if selected_members:
                import json
                self.monitor_input.setText(json.dumps(selected_members, ensure_ascii=False))
            else:
                self.monitor_input.setText("")

class EditListDialog(QDialog):
    def __init__(self, title, text='', parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setFixedWidth(350)
        layout = QVBoxLayout(self)
        self.text_edit = QTextEdit()
        self.text_edit.setPlainText(text)
        layout.addWidget(self.text_edit)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)
    def get_lines(self):
        return [line.strip() for line in self.text_edit.toPlainText().splitlines() if line.strip()]

class MonitorMemberDialog(QDialog):
    def __init__(self, parent, account_data):
        super().__init__(parent)
        self.setWindowTitle("监控会员管理")
        self.resize(600, 500)
        self.account_data = account_data
        self.domain = account_data.get('domain', '').rstrip('/')
        self.admin_info = account_data.get('admin_info')
        self.login_type = account_data.get('login_type', '登3')
        
        layout = QVBoxLayout(self)
        
        # 当前监控会员显示
        current_label = QLabel("当前监控会员:")
        layout.addWidget(current_label)
        
        self.current_table = QTableWidget()
        self.current_table.setColumnCount(2)
        self.current_table.setHorizontalHeaderLabels(["会员ID", "会员账号"])
        self.current_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.current_table.setMaximumHeight(150)
        # 设置选择模式为整行选择，并禁止选择表头
        self.current_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.current_table.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)
        self.current_table.horizontalHeader().setHighlightSections(False)
        layout.addWidget(self.current_table)
        
        # 按钮行
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("添加会员")
        self.remove_btn = QPushButton("移除选中")
        self.clear_btn = QPushButton("清空全部")
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.remove_btn)
        btn_layout.addWidget(self.clear_btn)
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        # 可选会员列表
        available_label = QLabel("可选会员列表:")
        layout.addWidget(available_label)
        
        self.available_table = QTableWidget()
        self.available_table.setColumnCount(2)
        self.available_table.setHorizontalHeaderLabels(["会员ID", "会员账号"])
        self.available_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        # 设置选择模式为整行选择，并禁止选择表头
        self.available_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.available_table.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)
        self.available_table.horizontalHeader().setHighlightSections(False)
        layout.addWidget(self.available_table)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.cancel_btn = QPushButton("取消")
        bottom_layout.addStretch()
        bottom_layout.addWidget(self.ok_btn)
        bottom_layout.addWidget(self.cancel_btn)
        layout.addLayout(bottom_layout)
        
        # 连接信号
        self.add_btn.clicked.connect(self.add_selected_members)
        self.remove_btn.clicked.connect(self.remove_selected_members)
        self.clear_btn.clicked.connect(self.clear_all_members)
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        self.available_table.cellDoubleClicked.connect(self.add_double_clicked_member)
        
        # 加载数据
        self.load_current_members()
        self.load_available_members()
    
    def get_main_window(self):
        """获取主窗口（CrawlerWindow）"""
        parent = self.parent()
        # 如果parent是AddAccountDialog，需要再获取它的parent
        while parent and not hasattr(parent, 'domain_get_ver_direct'):
            parent = parent.parent() if hasattr(parent, 'parent') else None
        return parent
    
    def get_account_from_main_window(self):
        """从主窗口的账号池中找到对应的账号"""
        main_window = self.get_main_window()
        if not main_window:
            return None
        
        # 通过admin_info中的uid来匹配账号
        uid = self.admin_info.get('uid', '')
        for account in main_window.account_pool:
            account_admin_info = account.get('admin_info', {})
            if account_admin_info.get('uid') == uid:
                return account
        return None
        
    def load_current_members(self):
        """加载当前监控的会员"""
        monitor_members = self.account_data.get('monitor_members', [])
        self.current_table.setRowCount(len(monitor_members))
        for i, member in enumerate(monitor_members):
            self.current_table.setItem(i, 0, QTableWidgetItem(str(member.get('id', ''))))
            self.current_table.setItem(i, 1, QTableWidgetItem(member.get('username', '')))
    
    def load_available_members(self):
        """加载可选的会员列表"""
        if not self.admin_info or not self.admin_info.get('session'):
            QMessageBox.warning(self, "错误", "账号未登录，无法获取会员列表")
            return
            
        try:
            # 获取主窗口（CrawlerWindow）
            main_window = self.get_main_window()
            if not main_window:
                QMessageBox.warning(self, "错误", "无法获取主窗口")
                return
            
            ver = getattr(main_window, 'global_ver', None)
            if not ver:
                # 如果没有Ver，自动从当前域名获取
                ver = main_window.domain_get_ver_direct(self.domain)
                if ver:
                    main_window.global_ver = ver
                    main_window.log(f"自动从域名 {self.domain} 获取到Ver: {ver}")
                else:
                    QMessageBox.warning(self, "错误", "无法从当前域名获取Ver信息")
                    return
            url = f"{self.domain}/transform.php?ver={ver}"
            querystring = {"ver": self.admin_info['ver']}
            login_layer = "ag" if self.login_type == "登3" else "su"
            
            # 构建payload
            up_id_value = self.admin_info.get('layer_id','') if self.login_type == "登3" else ""
            payload_parts = [
                f"p=get_acc_mem_list",
                f"ver={self.admin_info['ver']}",
                f"uid={self.admin_info['uid']}",
                f"login_layer={login_layer}",
                f"user_name=",
                f"enable=Y",
                f"ltype=ALL",
                f"sort_type=asc",
                f"sort_name=username",
                f"langx=zh-cn",
                f"up_id={up_id_value}"
            ]
            payload = "&".join(payload_parts)
            
            session = self.admin_info.get('session')
            cookies_str = "; ".join([f"{k}={v}" for k, v in session.cookies.get_dict().items()])
            headers = main_window.get_headers(self.domain, cookies=cookies_str)
            
            response = main_window.make_request(
                session=session,
                method="POST",
                url=url,
                params=querystring,
                data=payload,
                headers=headers,
                timeout=10,
                description="获取可选会员列表"
            )
            
            if not response:
                QMessageBox.warning(self, "错误", "获取会员列表请求失败")
                return
                
            json_data = response.json()
            
            # 检查是否是4X014会话过期错误
            if (json_data.get('status') == 'error' and json_data.get('code') == '4X014'):
                account = self.get_account_from_main_window()
                if account and main_window.check_session_expired(json_data, account):
                    # 重新登录成功，更新admin_info并重试
                    self.admin_info = account.get('admin_info')
                    self.load_available_members()  # 递归重试
                    return
                else:
                    QMessageBox.warning(self, "错误", "账号会话已过期且重新登录失败，请手动重新登录")
                    self.available_table.setRowCount(0)
                    return
            
            members = json_data.get('account', [])
            self.available_table.setRowCount(len(members))
            for i, mem in enumerate(members):
                mem_id = mem.get('id', '')
                username = mem.get('username', '')
                self.available_table.setItem(i, 0, QTableWidgetItem(str(mem_id)))
                self.available_table.setItem(i, 1, QTableWidgetItem(username))
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载会员列表失败: {e}")
    
    def add_selected_members(self):
        """添加选中的会员到监控列表"""
        selected_rows = self.available_table.selectionModel().selectedRows()
        for row in selected_rows:
            member_id = self.available_table.item(row.row(), 0).text()
            username = self.available_table.item(row.row(), 1).text()
            # 检查是否已存在
            if not self.is_member_exists(member_id):
                self.add_member_to_current(member_id, username)
    
    def add_double_clicked_member(self, row, col):
        """双击添加会员"""
        member_id = self.available_table.item(row, 0).text()
        username = self.available_table.item(row, 1).text()
        if not self.is_member_exists(member_id):
            self.add_member_to_current(member_id, username)
    
    def is_member_exists(self, member_id):
        """检查会员是否已存在于当前监控列表"""
        for i in range(self.current_table.rowCount()):
            if self.current_table.item(i, 0).text() == member_id:
                return True
        return False
    
    def add_member_to_current(self, member_id, username):
        """添加会员到当前监控列表"""
        row = self.current_table.rowCount()
        self.current_table.setRowCount(row + 1)
        self.current_table.setItem(row, 0, QTableWidgetItem(member_id))
        self.current_table.setItem(row, 1, QTableWidgetItem(username))
    
    def remove_selected_members(self):
        """移除选中的监控会员"""
        selected_rows = self.current_table.selectionModel().selectedRows()
        for row in sorted(selected_rows, key=lambda x: x.row(), reverse=True):
            self.current_table.removeRow(row.row())
    
    def clear_all_members(self):
        """清空所有监控会员"""
        reply = QMessageBox.question(self, '确认清空', '确定要清空所有监控会员吗？',
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                     QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.current_table.setRowCount(0)
    
    def get_monitor_members(self):
        """获取当前监控会员列表"""
        members = []
        for i in range(self.current_table.rowCount()):
            member_id = self.current_table.item(i, 0).text()
            username = self.current_table.item(i, 1).text()
            members.append({'id': member_id, 'username': username})
        return members

class MemberSelectForHistoryDialog(QDialog):
    def __init__(self, parent, monitor_members):
        super().__init__(parent)
        self.setWindowTitle("选择会员加载历史")
        self.resize(400, 300)
        self.monitor_members = monitor_members
        
        layout = QVBoxLayout(self)
        
        # 说明标签
        info_label = QLabel("请选择要加载历史记录的会员:")
        layout.addWidget(info_label)
        
        # 会员列表
        self.member_table = QTableWidget()
        self.member_table.setColumnCount(2)
        self.member_table.setHorizontalHeaderLabels(["会员ID", "会员账号"])
        self.member_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.member_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.member_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        # 禁止选择表头
        self.member_table.horizontalHeader().setHighlightSections(False)
        
        # 填充数据
        self.member_table.setRowCount(len(monitor_members))
        for i, member in enumerate(monitor_members):
            self.member_table.setItem(i, 0, QTableWidgetItem(str(member.get('id', ''))))
            self.member_table.setItem(i, 1, QTableWidgetItem(member.get('username', '')))
        
        layout.addWidget(self.member_table)
        
        # 按钮
        btn_layout = QHBoxLayout()
        self.load_btn = QPushButton("加载历史")
        self.cancel_btn = QPushButton("取消")
        btn_layout.addStretch()
        btn_layout.addWidget(self.load_btn)
        btn_layout.addWidget(self.cancel_btn)
        layout.addLayout(btn_layout)
        
        # 连接信号
        self.load_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        self.member_table.cellDoubleClicked.connect(self.accept)
        
    def get_selected_member_id(self):
        """获取选中的会员ID"""
        current_row = self.member_table.currentRow()
        if current_row >= 0:
            return self.member_table.item(current_row, 0).text()
        return None

class CrawlerWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.log_filename = f'日志_{datetime.now().strftime("%Y%m%d")}.log'
        self.setWindowTitle("HG Server V1.2 (多账号版) - PyQt6")
        self.resize(1280, 900)
        # 初始化域名列表和账号池
        self.domain_list = []
        self.account_pool = []
        self.global_ver = None  # 初始化全局Ver
        
        # 初始化Order.json文件
        self.init_order_json()
        
        self.init_ui()
        self.load_config()  # 启动时自动读取配置
        
        # 初始化运行时间相关变量
        self.start_time = None
        self.accumulated_time = 0  # 累积运行时间（秒）
        self.runtime_timer = QTimer()
        self.runtime_timer.timeout.connect(self.update_runtime_display)
        self.runtime_timer.setInterval(1000)  # 每秒更新一次
        
        # 立即显示初始运行时间并启动定时器
        self.update_runtime_display()
        self.runtime_timer.start()
        
        # 初始化重新登录线程管理
        self.relogin_threads = {}  # 管理重新登录线程

    def init_order_json(self):
        """初始化Order.json文件，如果不存在则创建空数组"""
        try:
            order_file = "Order.json"
            if not os.path.exists(order_file):
                # 文件不存在，创建空数组
                with open(order_file, 'w', encoding='ansi') as f:
                    json.dump([], f, ensure_ascii=False)
                print("已初始化Order.json文件")
            else:
                # 文件存在，检查是否为有效JSON
                try:
                    with open(order_file, 'r', encoding='ansi') as f:
                        data = json.load(f)
                    # 如果不是列表，重新初始化为空数组
                    if not isinstance(data, list):
                        with open(order_file, 'w', encoding='ansi') as f:
                            json.dump([], f, ensure_ascii=False)
                        print("Order.json格式异常，已重新初始化")
                except (json.JSONDecodeError, UnicodeDecodeError):
                    # JSON格式错误，重新创建
                    with open(order_file, 'w', encoding='ansi') as f:
                        json.dump([], f, ensure_ascii=False)
                    print("Order.json文件损坏，已重新初始化")
        except Exception as e:
            print(f"初始化Order.json失败: {str(e)}")

    def init_ui(self):
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)
        # 控制面板
        control_layout = QHBoxLayout()
        self.port_input = QLineEdit("8080")
        self.port_input.setMaximumWidth(80)
        self.delay_input = QLineEdit("1")
        self.delay_input.setMaximumWidth(80)
        self.only_monitor_checkbox = QCheckBox("仅监控")
        self.start_stop_button = QPushButton("启动服务")
        self.save_config_button = QPushButton("保存配置")
        self.save_config_button.setMaximumWidth(100)
        self.clear_log_button = QPushButton("清除日志")
        self.agent_load_button = QPushButton("载入域名")
        self.add_account_button = QPushButton("添加账号")
        self.batch_login_button = QPushButton("一键登录")
        self.batch_login_button.clicked.connect(self.batch_login_accounts)
        control_layout.addWidget(QLabel("端口:"))
        control_layout.addWidget(self.port_input)
        control_layout.addWidget(QLabel("监控延迟(秒):"))
        control_layout.addWidget(self.delay_input)
        control_layout.addWidget(self.only_monitor_checkbox)
        control_layout.addWidget(self.start_stop_button)
        
        control_layout.addWidget(self.agent_load_button)
        control_layout.addWidget(self.add_account_button)
        control_layout.addWidget(self.batch_login_button)
        control_layout.addWidget(self.save_config_button)
        control_layout.addWidget(self.clear_log_button)
        control_layout.addStretch()
        layout.addLayout(control_layout)

        # Tab 控件: 代理账号, 下单数据, 历史数据, 账号历史, 历史详情
        self.tab_widget = QTabWidget()
        
        # 代理账号Tab (原账号池)
        self.account_widget = QWidget()
        account_layout = QVBoxLayout(self.account_widget)
        # 账号池Tab（移除导入账号、测试按钮，仅保留表格）
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(6)
        self.account_table.setHorizontalHeaderLabels(["域名", "账号", "账号状态", "运行状态", "监控消息", "操作"])
        # 禁止整个表格的编辑
        self.account_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        header = self.account_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Interactive)  # 域名
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Interactive)  # 账号
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Interactive)  # 账号状态
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Interactive)  # 运行状态
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)      # 监控消息 - 自动适应宽度
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Interactive)  # 操作
        # 设置代理账号表格列宽
        self.account_table.setColumnWidth(0, 250)
        self.account_table.setColumnWidth(1, 120)
        self.account_table.setColumnWidth(2, 75)
        self.account_table.setColumnWidth(3, 75)
        # 监控消息列使用Stretch模式，不设置固定宽度
        self.account_table.setColumnWidth(5, 400)  # 增加操作列宽度，容纳更多按钮
        account_layout.addWidget(self.account_table)
        
        # 其他Tab
        columns = ["订单号", "比赛信息", "下单类型", "下单玩法", "下单盘口", "下单金额", "下单比分", "下单会员", "代理账号"]
        self.order_table = QTableWidget()
        self.order_table.setColumnCount(len(columns))
        self.order_table.setHorizontalHeaderLabels(columns)
        header = self.order_table.horizontalHeader()
        # 设置下单数据表格列宽
        self.order_table.setColumnWidth(0, 120)  # 订单号
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 比赛信息 - 自适应
        self.order_table.setColumnWidth(2, 150)  # 下单类型
        self.order_table.setColumnWidth(3, 120)  # 下单玩法
        self.order_table.setColumnWidth(4, 150)  # 下单盘口
        self.order_table.setColumnWidth(5, 80)   # 下单金额
        self.order_table.setColumnWidth(6, 80)   # 下单比分
        self.order_table.setColumnWidth(7, 150)  # 下单会员
        self.order_table.setColumnWidth(8, 120)   # 代理账号
        self.acc_history_widget = QWidget()
        acc_layout = QVBoxLayout(self.acc_history_widget)
        self.acc_summary_label = QLabel("总投注: -, 有效投注: -, 派彩结果: -")
        acc_layout.addWidget(self.acc_summary_label)
        self.acc_history_table = QTableWidget()
        acc_columns = ["日期", "星期", "投注额", "有效投注", "派彩结果", "操作"]
        self.acc_history_table.setColumnCount(len(acc_columns))
        self.acc_history_table.setHorizontalHeaderLabels(acc_columns)
        acc_layout.addWidget(self.acc_history_table)
        self.detail_widget = QWidget()
        detail_layout = QVBoxLayout(self.detail_widget)
        detail_columns = ["订单号", "比赛信息", "下单金额", "下单水位", "下单时间", "下单盘口", "输赢"]
        self.detail_table = QTableWidget()
        self.detail_table.setColumnCount(len(detail_columns))
        self.detail_table.setHorizontalHeaderLabels(detail_columns)
        dt_header = self.detail_table.horizontalHeader()
        # 设置列宽: 比赛信息300px, 下单盘口250px, 输赢列自适应
        dt_header.resizeSection(1, 300)  # 比赛信息
        dt_header.resizeSection(5, 250)  # 下单盘口
        dt_header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)  # 输赢列自适应
        detail_layout.addWidget(self.detail_table)
        
        # 域名列表Tab
        self.domain_list_widget = QWidget()
        domain_layout = QVBoxLayout(self.domain_list_widget)
        self.domain_list_table = QTableWidget()
        self.domain_list_table.setColumnCount(3)
        self.domain_list_table.setHorizontalHeaderLabels(["可用域名", "消息", "操作"])
        self.domain_list_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.domain_list_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.domain_list_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.domain_list_table.setColumnWidth(2, 180)
        domain_layout.addWidget(self.domain_list_table)
        
        # 推送设置Tab
        self.push_widget = QWidget()
        push_layout = QHBoxLayout(self.push_widget)
        push_layout.setContentsMargins(8, 8, 8, 8)
        push_layout.setSpacing(24)

        # WxPusher推送设置（左侧）
        wx_group = QGroupBox("WxPusher推送设置")
        wx_layout = QFormLayout(wx_group)
        wx_layout.setVerticalSpacing(8)
        wx_layout.setLabelAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        wx_layout.setFormAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        self.push_enable_checkbox = QCheckBox("启用WxPusher推送")
        self.push_amount_input = QLineEdit()
        self.push_amount_input.setPlaceholderText("推送金额")
        self.push_amount_input.setMaximumWidth(200)
        wx_layout.addRow("是否推送：", self.push_enable_checkbox)
        wx_layout.addRow("推送金额：", self.push_amount_input)
        # 先创建推送UID输入框及编辑按钮
        self.push_uid_input = QLineEdit()
        self.push_uid_input.setPlaceholderText("推送UID，输入的时候一行一个")
        self.push_uid_input.setReadOnly(True)
        self.push_uid_input.setMaximumWidth(200)
        self.push_uid_edit_btn = QPushButton("编辑")
        self.push_uid_edit_btn.setMaximumWidth(60)
        self.push_uid_edit_btn.clicked.connect(self.edit_uid_list)
        uid_row = QHBoxLayout()
        uid_row.addWidget(self.push_uid_input)
        uid_row.addSpacing(8)
        uid_row.addWidget(self.push_uid_edit_btn)
        # 先创建测试按钮
        self.push_test_btn = QPushButton("测试推送")
        self.push_test_btn.setMaximumWidth(120)
        # 用VBoxLayout将UID和按钮放到底部
        wx_bottom = QVBoxLayout()
        wx_bottom.addLayout(uid_row)
        wx_bottom.addWidget(self.push_test_btn)
        wx_layout.addRow("推送UID：", wx_bottom)
        push_layout.addWidget(wx_group, 1)

        # 邮件推送设置（右侧）
        mail_group = QGroupBox("邮件推送设置")
        mail_layout = QFormLayout(mail_group)
        mail_layout.setVerticalSpacing(8)
        self.mail_enable_checkbox = QCheckBox("启用邮件推送")
        self.mail_from_input = QLineEdit()
        self.mail_from_input.setPlaceholderText("发件人邮箱")
        self.mail_from_input.setMaximumWidth(200)
        self.mail_smtp_input = QLineEdit()
        self.mail_smtp_input.setPlaceholderText("SMTP服务器地址")
        self.mail_smtp_input.setMaximumWidth(200)
        self.mail_port_input = QLineEdit()
        self.mail_port_input.setPlaceholderText("端口号，如465或587")
        self.mail_port_input.setMaximumWidth(200)
        self.mail_pass_input = QLineEdit()
        self.mail_pass_input.setPlaceholderText("邮箱授权码/密码")
        self.mail_pass_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.mail_pass_input.setMaximumWidth(200)
        mail_layout.addRow("是否推送：", self.mail_enable_checkbox)
        mail_layout.addRow("发件人邮箱：", self.mail_from_input)
        mail_layout.addRow("SMTP服务器：", self.mail_smtp_input)    
        mail_layout.addRow("端口：", self.mail_port_input)
        mail_layout.addRow("授权码/密码：", self.mail_pass_input)
        # 收件人邮箱输入框及编辑按钮
        self.mail_to_input = QLineEdit()
        self.mail_to_input.setPlaceholderText("收件人邮箱，输入的时候一行一个")
        self.mail_to_input.setReadOnly(True)
        self.mail_to_input.setMaximumWidth(200)
        self.mail_to_edit_btn = QPushButton("编辑")
        self.mail_to_edit_btn.setMaximumWidth(60)
        self.mail_to_edit_btn.clicked.connect(self.edit_mail_list)
        mail_row = QHBoxLayout()
        mail_row.addWidget(self.mail_to_input)
        mail_row.addSpacing(8)
        mail_row.addWidget(self.mail_to_edit_btn)
        # 测试按钮
        self.mail_test_btn = QPushButton("测试邮件推送")
        self.mail_test_btn.setMaximumWidth(120)
        mail_bottom = QVBoxLayout()
        mail_bottom.addLayout(mail_row)
        mail_bottom.addWidget(self.mail_test_btn)
        mail_layout.addRow("收件人邮箱：", mail_bottom)
        push_layout.addWidget(mail_group, 1)
        
        # 添加所有Tab
        self.tab_widget.addTab(self.account_widget, "代理账号")
        self.tab_widget.addTab(self.order_table, "下单数据")
        self.tab_widget.addTab(self.acc_history_widget, "账号历史")
        self.tab_widget.addTab(self.detail_widget, "历史详情")
        self.tab_widget.addTab(self.domain_list_widget, "域名列表")
        self.tab_widget.addTab(self.push_widget, "推送设置")
        
        # 设置Tab控件占据更多空间
        layout.addWidget(self.tab_widget, 7)
        
        # 日志区域和底部状态栏
        log_container = QWidget()
        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加一个标签，使日志区域更明显
        log_header = QHBoxLayout()
        log_label = QLabel("运行日志")
        log_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        log_header.addWidget(log_label)
        log_header.addStretch()
        log_layout.addLayout(log_header)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # 日志区域占比较小
        layout.addWidget(log_container, 3)
        
        bottom_layout = QHBoxLayout()
        self.runtime_label = QLabel("运行时间: 0天0小时0分0秒")
        self.author_label = QLabel("By: _0xAiCode")
        font = QFont()
        font.setPointSize(10)
        self.runtime_label.setFont(font)
        self.author_label.setFont(font)
        bottom_layout.addWidget(self.runtime_label)
        bottom_layout.addWidget(self.author_label, alignment=Qt.AlignmentFlag.AlignRight)
        layout.addLayout(bottom_layout)
        
        # 美化风格
        self.setStyleSheet('''
            QWidget { background-color: #f7fafd; }
            QTableWidget { background-color: #fff; border: 1px solid #d0e3f1; gridline-color: #e0e0e0; }
            QTableWidget::item:selected { background-color: #b3e5fc; color: #222; }
            QPushButton { background-color: #2196F3; color: white; border-radius: 4px; padding: 6px 12px; }
            QPushButton:hover { background-color: #1976D2; }
            QComboBox { background-color: white; border: 1px solid #b3d9ff; padding: 2px; }
            QLineEdit, QTextEdit { background: #f5faff; border: 1px solid #b3d9ff; }
            QLabel { color: #333; }
            QTabWidget::pane { border: 1px solid #d0e3f1; }
            QTabBar::tab { background-color: #e1f5fe; padding: 8px 12px; margin-right: 2px; }
            QTabBar::tab:selected { background-color: #29b6f6; color: white; }
            QTabBar::tab:hover:!selected { background-color: #b3e5fc; }
        ''')
        self.setCentralWidget(main_widget)
        self.add_account_button.clicked.connect(self.show_add_account_dialog)
        self.push_test_btn.clicked.connect(self.test_wx_push)
        self.mail_test_btn.clicked.connect(self.test_mail_push)
        self.agent_load_button.clicked.connect(self.load_agent_domains)
        self.save_config_button.clicked.connect(self.save_config)
        self.clear_log_button.clicked.connect(self.clear_log)
        self.start_stop_button.clicked.connect(self.toggle_service)

    def show_add_account_dialog(self):
        dialog = AddAccountDialog(self)
        while True:
            if dialog.exec() == QDialog.DialogCode.Accepted:
                data = dialog.get_data()
                
                # 验证必填字段
                if not all([data["domain"], data["account"], data["password"]]):
                    QMessageBox.warning(self, "提示", "域名、账号和密码不能为空！")
                    continue  # 继续显示对话框，不关闭
                    
                # 解析监控会员数据
                monitor_members = []
                monitor_text = data["monitor"].strip()
                if monitor_text:
                    try:
                        import json
                        # 尝试JSON格式
                        monitor_members = json.loads(monitor_text)
                    except:
                        # 如果不是JSON，假设是逗号分隔的ID列表（兼容旧格式）
                        member_ids = [mid.strip() for mid in monitor_text.split(',') if mid.strip()]
                        # 注意：这里使用临时的username格式，实际应该通过API获取真实用户名
                        monitor_members = [{'id': mid, 'username': f'会员{mid}'} for mid in member_ids]
                
                if not monitor_members:
                    QMessageBox.warning(self, "提示", "请设置监控会员！")
                    continue  # 继续显示对话框，不关闭
                
                # 将monitor_members添加到data中，保留原始monitor字段用于兼容
                data['monitor_members'] = monitor_members
                
                # 如果弹窗中有登录信息，传递到账号池中
                if hasattr(dialog, 'admin_info') and dialog.admin_info:
                    data['admin_info'] = dialog.admin_info
                    data['status'] = '已登录'
                else:
                    data['status'] = '未登录'
                
                # 添加到账号池并更新表格
                self.account_pool.append(data)
                self.update_account_table()
                
                # 生成监控会员摘要用于日志
                member_summary = ", ".join([m.get('username', f"会员{m.get('id')}") for m in monitor_members[:3]])
                if len(monitor_members) > 3:
                    member_summary += f" 等{len(monitor_members)}个会员"
                
                self.log(f"账号 {data['account']} 已添加到账号池，监控会员: {member_summary}")
                QMessageBox.information(self, "成功", f"账号 {data['account']} 已添加到账号池，监控 {len(monitor_members)} 个会员")
                break  # 成功添加后退出循环
            else:
                break  # 用户点击取消，退出循环

    def update_account_table(self):
        """更新代理账号表格显示"""
        self.account_table.setRowCount(len(self.account_pool))
        # 设置表格行高，确保按钮有足够空间
        for i in range(len(self.account_pool)):
            self.account_table.setRowHeight(i, 40)
        for i, account in enumerate(self.account_pool):
            # 域名、账号
            self.account_table.setItem(i, 0, QTableWidgetItem(account.get('domain', '')))
            self.account_table.setItem(i, 1, QTableWidgetItem(account.get('account', '')))
            
            # 账号状态 - 文字颜色高亮
            account_status = account.get('status', '未登录')
            account_status_item = QTableWidgetItem(account_status)
            if account_status == '已登录':
                account_status_item.setForeground(QBrush(QColor(0, 128, 0)))     # 绿色文字
            elif account_status == '登录失败':
                account_status_item.setForeground(QBrush(QColor(255, 0, 0)))     # 红色文字
            else:  # 未登录
                account_status_item.setForeground(QBrush(QColor(128, 128, 128))) # 灰色文字
            self.account_table.setItem(i, 2, account_status_item)
            
            # 运行状态 - 文字颜色高亮
            running_status = account.get('running_status', '未运行')
            running_status_item = QTableWidgetItem(running_status)
            if running_status == '运行中':
                running_status_item.setForeground(QBrush(QColor(0, 128, 0)))     # 绿色文字
            elif running_status == '异常':
                running_status_item.setForeground(QBrush(QColor(255, 0, 0)))     # 红色文字
            else:  # 未运行
                running_status_item.setForeground(QBrush(QColor(128, 128, 128))) # 灰色文字
            self.account_table.setItem(i, 3, running_status_item)
            
            # 监控消息
            monitor_members = account.get('monitor_members', [])
            if monitor_members:
                member_names = [m.get('username', '') for m in monitor_members]
                monitor_msg = f"监控 {len(monitor_members)} 个会员: {', '.join(member_names[:3])}"
                if len(monitor_members) > 3:
                    monitor_msg += "..."
            else:
                monitor_msg = "未设置监控会员"
            self.account_table.setItem(i, 4, QTableWidgetItem(monitor_msg))
            
            # 操作列 - 添加操作按钮，居中平铺
            op_widget = QWidget()
            op_layout = QHBoxLayout(op_widget)
            op_layout.setContentsMargins(8, 4, 8, 4)
            op_layout.setSpacing(6)
            
            # 左侧弹性空间
            op_layout.addStretch()
            
            login_btn = QPushButton("登录")
            login_btn.setFixedSize(55, 28)
            login_btn.clicked.connect(lambda _, row=i: self.login_account(row))
            
            edit_btn = QPushButton("编辑")
            edit_btn.setFixedSize(55, 28)
            edit_btn.clicked.connect(lambda _, row=i: self.edit_account(row))
            
            monitor_btn = QPushButton("监控会员")
            monitor_btn.setFixedSize(75, 28)
            monitor_btn.clicked.connect(lambda _, row=i: self.manage_monitor_members(row))
            
            history_btn = QPushButton("加载历史")
            history_btn.setFixedSize(75, 28)
            history_btn.clicked.connect(lambda _, row=i: self.load_account_history(row))
            
            delete_btn = QPushButton("删除")
            delete_btn.setFixedSize(55, 28)
            delete_btn.clicked.connect(lambda _, row=i: self.delete_account(row))
            
            op_layout.addWidget(login_btn)
            op_layout.addWidget(edit_btn)
            op_layout.addWidget(monitor_btn)
            op_layout.addWidget(history_btn)
            op_layout.addWidget(delete_btn)
            
            # 右侧弹性空间
            op_layout.addStretch()
            
            self.account_table.setCellWidget(i, 5, op_widget)

    def login_account(self, row):
        """登录指定行的账号"""
        if row >= len(self.account_pool):
            return
        account = self.account_pool[row]
        result = self.admin_login(account)
        if result:
            account['admin_info'] = result
            account['status'] = '已登录'
            self.update_account_table()
            # admin_login方法已经输出了登录成功日志，这里不需要重复输出
        else:
            account['status'] = '登录失败'
            self.update_account_table()

    def edit_account(self, row):
        """编辑指定行的账号"""
        if row >= len(self.account_pool):
            return
        account = self.account_pool[row]
        
        # 创建编辑对话框，预填充当前账号信息
        dialog = AddAccountDialog(self)
        dialog.setWindowTitle("编辑账号")
        
        # 预填充数据
        dialog.domain_input.setCurrentText(account.get('domain', ''))
        dialog.login_type_combo.setCurrentText(account.get('login_type', '登3'))
        dialog.account_input.setText(account.get('account', ''))
        dialog.password_input.setText(account.get('password', ''))
        dialog.security_code_input.setText(account.get('security_code', ''))
        
        # 处理监控会员数据的预填充
        monitor_members = account.get('monitor_members', [])
        if monitor_members:
            import json
            dialog.monitor_input.setText(json.dumps(monitor_members, ensure_ascii=False))
        else:
            # 兼容旧格式
            dialog.monitor_input.setText(account.get('monitor', ''))
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_data = dialog.get_data()
            if not all([new_data["domain"], new_data["account"], new_data["password"]]):
                QMessageBox.warning(self, "提示", "域名、账号和密码不能为空！")
                return
            
            # 解析监控会员数据
            monitor_members = []
            monitor_text = new_data["monitor"].strip()
            if monitor_text:
                try:
                    import json
                    # 尝试JSON格式
                    monitor_members = json.loads(monitor_text)
                except:
                    # 如果不是JSON，假设是逗号分隔的ID列表（兼容旧格式）
                    member_ids = [mid.strip() for mid in monitor_text.split(',') if mid.strip()]
                    # 注意：这里使用临时的username格式，实际应该通过API获取真实用户名
                    monitor_members = [{'id': mid, 'username': f'会员{mid}'} for mid in member_ids]
            
            if not monitor_members:
                QMessageBox.warning(self, "提示", "请设置监控会员！")
                return
            
            # 将monitor_members添加到new_data中
            new_data['monitor_members'] = monitor_members
            
            # 更新账号信息，保留原有的非敏感状态
            old_account = account.get('account', '')
            self.account_pool[row].update(new_data)
            # 重置登录状态，因为账号信息可能已变更
            self.account_pool[row]['status'] = '未登录'
            self.account_pool[row].pop('admin_info', None)
            
            self.update_account_table()
            
            # 生成监控会员摘要用于日志
            member_summary = ", ".join([m.get('username', f"会员{m.get('id')}") for m in monitor_members[:3]])
            if len(monitor_members) > 3:
                member_summary += f" 等{len(monitor_members)}个会员"
            
            self.log(f"账号 {old_account} 信息已更新，监控会员: {member_summary}")


    def delete_account(self, row):
        """删除指定行的账号"""
        if row >= len(self.account_pool):
            return
        account = self.account_pool[row]
        reply = QMessageBox.question(self, '确认删除', f'确定要删除账号 {account["account"]} 吗？',
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                                     QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            removed_account = self.account_pool.pop(row)
            self.update_account_table()
            self.log(f"账号 {removed_account['account']} 已从账号池中删除")

    def manage_monitor_members(self, row):
        """管理指定账号的监控会员"""
        if row >= len(self.account_pool):
            return
        account = self.account_pool[row]
        
        # 检查账号是否已登录
        if account.get('status') != '已登录' or not account.get('admin_info'):
            reply = QMessageBox.question(self, '需要登录', 
                                       f'账号 {account["account"]} 未登录，是否先登录后再管理监控会员？',
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.Yes)
            if reply == QMessageBox.StandardButton.Yes:
                # 先登录账号
                result = self.admin_login(account)
                if result:
                    account['admin_info'] = result
                    account['status'] = '已登录'
                    self.update_account_table()
                else:
                    account['status'] = '登录失败'
                    self.update_account_table()
                    QMessageBox.warning(self, "登录失败", "账号登录失败，无法管理监控会员")
                    return
            else:
                return
        
        # 打开监控会员管理对话框
        dialog = MonitorMemberDialog(self, account)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 保存监控会员信息
            monitor_members = dialog.get_monitor_members()
            account['monitor_members'] = monitor_members
            self.log(f"账号 {account['account']} 监控会员已更新，共 {len(monitor_members)} 个会员")

    def load_account_history(self, row):
        """加载指定账号的历史记录"""
        if row >= len(self.account_pool):
            return
        account = self.account_pool[row]
        
        # 检查是否有监控会员
        monitor_members = account.get('monitor_members', [])
        if not monitor_members:
            QMessageBox.warning(self, "提示", f"账号 {account['account']} 未设置监控会员，请先设置监控会员")
            return
        
        # 检查账号是否已登录
        if account.get('status') != '已登录' or not account.get('admin_info'):
            QMessageBox.warning(self, "提示", f"账号 {account['account']} 未登录，请先登录账号")
            return
        
        # 根据监控会员数量决定是否弹出选择对话框
        selected_member_id = None
        if len(monitor_members) == 1:
            # 只有一个会员，直接加载
            selected_member_id = monitor_members[0].get('id')
        else:
            # 多个会员，弹出选择对话框
            dialog = MemberSelectForHistoryDialog(self, monitor_members)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                selected_member_id = dialog.get_selected_member_id()
            else:
                return  # 用户取消
        
        if not selected_member_id:
            QMessageBox.warning(self, "错误", "未选择有效的会员ID")
            return
        
        # 执行历史记录加载
        self.load_member_history(account, selected_member_id)

    def load_member_history(self, account, member_id):
        """加载指定会员的历史记录"""
        admin_info = account.get('admin_info')
        domain = account.get('domain', '')
        login_type = account.get('login_type', '登3')
        login_layer = "ag" if login_type == "登3" else "su"
        
        ver = getattr(self, 'global_ver', None)
        if not ver:
            QMessageBox.warning(self, "错误", "未获取到Ver信息")
            return
        
        url = f"{domain.rstrip('/')}/transform.php?ver={ver}"
        payload = {
            'login_layer': login_layer,
            'uid': admin_info.get('uid', ''),
            'langx': 'zh-cn',
            'ver': ver,
            'p': 'get_history_data',
            'mid': member_id,
            'selGtype': 'ALL'
        }
        
        # 使用session获取cookies
        session = admin_info.get('session')
        if session:
            cookies_str = "; ".join([f"{k}={v}" for k, v in session.cookies.get_dict().items()])
        else:
            cookies_str = admin_info.get("set_cookie", "")
            if not cookies_str:
                cookies_str = "; ".join([f"{k}={v}" for k, v in admin_info.get("cookies", {}).items()])
        
        headers = self.get_headers(domain, cookies=cookies_str)
        
        # 使用封装的请求函数
        response = self.make_request(
            session=session,
            method="POST",
            url=url,
            data=payload,
            headers=headers,
            timeout=10,
            description=f"加载会员 {member_id} 的历史记录"
        )
        
        if not response:
            QMessageBox.warning(self, "错误", "获取历史记录请求失败")
            return
        
        try:
            data = response.json()
            
            # 检查是否是4X014会话过期错误
            if self.check_session_expired(data, account):
                # 重新登录成功，递归重试
                self.load_member_history(account, member_id)
                return
            elif data.get('status') == 'error' and data.get('code') == '4X014':
                # 重新登录失败
                QMessageBox.warning(self, "错误", "账号会话已过期且重新登录失败，请手动重新登录")
                return
            
            if data.get('status') != '200':
                self.log(f"获取账号历史失败: {data}")
                QMessageBox.warning(self, "错误", f"获取历史记录失败: {data.get('msg', '未知错误')}")
                return
            
            # 解析历史记录数据
            history = data.get('history', [])
            total = data.get('total', {})
            gold = total.get('GOLD', '-')
            vgold = total.get('VGOLD', '-')
            winloss = total.get('WINLOSS', '-')
            
            # 找到会员名称
            member_name = "未知"
            for member in account.get('monitor_members', []):
                if str(member.get('id')) == str(member_id):
                    member_name = member.get('username', '未知')
                    break
            
            # 更新汇总信息
            self.acc_summary_label.setText(f"总投注: {gold}, 有效投注: {vgold}, 派彩结果: {winloss}")
            
            # 填充历史记录表格
            self.acc_history_table.setRowCount(len(history))
            for i, item in enumerate(history):
                date_name = item.get('DATE_NAME', '')
                week = item.get('WEEK', '')
                gold_item = item.get('GOLD', '-')
                vgold_item = item.get('VGOLD', '-')
                win_item = item.get('WINLOSS', '-')
                date_full = item.get('DATE', '')
                
                self.acc_history_table.setItem(i, 0, QTableWidgetItem(date_name))
                self.acc_history_table.setItem(i, 1, QTableWidgetItem(week))
                self.acc_history_table.setItem(i, 2, QTableWidgetItem(gold_item))
                self.acc_history_table.setItem(i, 3, QTableWidgetItem(vgold_item))
                
                # 派彩结果带颜色显示
                win_item_cell = QTableWidgetItem(win_item)
                try:
                    if win_item not in ['-', '']:
                        val = float(win_item.replace(',', ''))
                        if val < 0:
                            win_item_cell.setForeground(QBrush(QColor('red')))
                        else:
                            win_item_cell.setForeground(QBrush(QColor('green')))
                except:
                    pass
                self.acc_history_table.setItem(i, 4, win_item_cell)
                
                # 操作列 - 详情按钮
                detail_btn = QPushButton("详情")
                detail_btn.clicked.connect(lambda _, m=member_id, d=date_full, acc=account: self.load_history_detail(acc, m, d))
                self.acc_history_table.setCellWidget(i, 5, detail_btn)
            
            # 切换到账号历史Tab
            self.tab_widget.setCurrentWidget(self.acc_history_widget)
            
            self.log(f"已加载账号 {account['account']} 的会员 {member_name}(ID:{member_id}) 历史记录，共 {len(history)} 条")
            
        except Exception as e:
            self.log(f"解析历史记录数据失败: {e}")
            QMessageBox.warning(self, "错误", f"解析历史记录数据失败: {e}")

    def load_history_detail(self, account, member_id, date_full, pay_type='1', retry_count=0):
        """加载历史详情到历史详情Tab"""
        # 切换到历史详情Tab并清空内容
        if retry_count == 0:  # 只在第一次调用时清空界面
            self.tab_widget.setCurrentWidget(self.detail_widget)
            self.detail_table.clearContents()
            self.detail_table.setRowCount(0)
        
        admin_info = account.get('admin_info')
        domain = account.get('domain', '')
        login_type = account.get('login_type', '登3')
        login_layer = "ag" if login_type == "登3" else "su"
        
        ver = getattr(self, 'global_ver', None)
        if not ver:
            QMessageBox.warning(self, "错误", "未获取到Ver信息")
            return
        
        url = f"{domain.rstrip('/')}/transform.php?ver={ver}"
        payload = {
            'login_layer': login_layer,
            'uid': admin_info.get('uid', ''),
            'langx': 'zh-cn',
            'ver': ver,
            'p': 'get_history_view',
            'mid': member_id,
            'selGtype': 'ALL',
            'today_gmt': date_full,
            'tmp_flag': 'Y',
            'pay_type': pay_type
        }
        
        # 使用session获取cookies
        session = admin_info.get('session')
        if session:
            cookies_str = "; ".join([f"{k}={v}" for k, v in session.cookies.get_dict().items()])
        else:
            cookies_str = admin_info.get("set_cookie", "")
            if not cookies_str:
                cookies_str = "; ".join([f"{k}={v}" for k, v in admin_info.get("cookies", {}).items()])
        
        headers = self.get_headers(domain, cookies=cookies_str)
        
        # 使用封装的请求函数
        response = self.make_request(
            session=session,
            method="POST",
            url=url,
            data=payload,
            headers=headers,
            timeout=10,
            description=f"加载会员 {member_id} 日期 {date_full} 的历史详情"
        )
        
        if not response:
            QMessageBox.warning(self, "错误", "获取历史详情请求失败")
            return
        
        try:
            data = response.json()
            
            # 检查是否是4X014会话过期错误
            if self.check_session_expired(data, account):
                # 重新登录成功，递归重试
                self.load_history_detail(account, member_id, date_full, pay_type, retry_count)
                return
            elif data.get('status') == 'error' and data.get('code') == '4X014':
                # 重新登录失败
                QMessageBox.warning(self, "错误", "账号会话已过期且重新登录失败，请手动重新登录")
                self.detail_table.setRowCount(0)
                return
            
            # 检查是否是pay_type参数错误
            if (data.get('status') == 'error' and 
                data.get('code') == 'error' and 
                data.get('msg') == '' and 
                retry_count == 0):
                # pay_type参数错误，切换参数重试
                new_pay_type = '0' if pay_type == '1' else '1'
                self.log(f"pay_type参数 '{pay_type}' 错误，尝试使用 '{new_pay_type}' 重新获取历史详情")
                self.load_history_detail(account, member_id, date_full, new_pay_type, retry_count + 1)
                return
            elif (data.get('status') == 'error' and 
                  data.get('code') == 'error' and 
                  data.get('msg') == '' and 
                  retry_count > 0):
                # 重试后仍然失败，直接返回
                self.log(f"pay_type参数重试后仍然失败，停止获取历史详情")
                QMessageBox.warning(self, "错误", "pay_type参数错误，无法获取历史详情")
                self.detail_table.setRowCount(0)
                return
            
            if data.get('status') != '200':
                self.log(f"获取历史详情失败: {data}")
                QMessageBox.warning(self, "错误", f"获取历史详情失败: {data.get('msg', '未知错误')}")
                self.detail_table.setRowCount(0)
                return
            
            wagers = data.get('wagers', [])
            self.detail_table.setRowCount(len(wagers))
            
            for i, item in enumerate(wagers):
                wid = item.get('WID', '')
                league = item.get('LEAGUE', '').strip()
                team_h = item.get('TEAM_H', '').strip()
                team_c = item.get('TEAM_C', '').strip()
                match_info = f"{league} - {team_h} - {team_c}"
                gold = item.get('GOLD', '')
                ioratio = item.get('IORATIO', '')
                addtime = item.get('ADDTIME', '')
                result = item.get('RESULT', '').strip().replace("<tt class='word_red'>", '').replace("</tt>", '')
                bet_ratio = item.get('BET_RATIO', '')
                market = f"{result}{bet_ratio}" if bet_ratio else result
                result_wl = item.get('RESULT_WL', '').strip()
                
                self.detail_table.setItem(i, 0, QTableWidgetItem(wid))
                self.detail_table.setItem(i, 1, QTableWidgetItem(match_info))
                self.detail_table.setItem(i, 2, QTableWidgetItem(gold))
                self.detail_table.setItem(i, 3, QTableWidgetItem(ioratio))
                self.detail_table.setItem(i, 4, QTableWidgetItem(addtime))
                self.detail_table.setItem(i, 5, QTableWidgetItem(market))
                
                # 输赢结果带颜色显示
                cell = QTableWidgetItem(result_wl)
                if result_wl:
                    if "输" in result_wl:
                        cell.setForeground(QBrush(QColor('red')))
                    elif "赢" in result_wl:
                        cell.setForeground(QBrush(QColor('green')))
                self.detail_table.setItem(i, 6, cell)
            
            # 找到会员名称用于日志
            member_name = "未知"
            for member in account.get('monitor_members', []):
                if str(member.get('id')) == str(member_id):
                    member_name = member.get('username', '未知')
                    break
            
            self.log(f"已加载历史详情: 会员{member_name}(ID={member_id}) 日期={date_full} 共{len(wagers)}条")
            
        except Exception as e:
            self.log(f"解析历史详情数据失败: {e}")
            QMessageBox.warning(self, "错误", f"解析历史详情数据失败: {e}")

    def check_session_expired(self, response_data, account):
        """检查会话是否过期，如果过期则自动重新登录"""
        if (response_data.get('status') == 'error' and 
            response_data.get('code') in ['4X014', '4X042']):
            self.log(f"账号 {account.get('account', '')} 会话已过期(错误码: {response_data.get('code')}), 正在重新登录...")
            
            # 重新登录
            new_admin_info = self.admin_login(account)
            if new_admin_info:
                # 更新账号池中的登录信息
                account['admin_info'] = new_admin_info
                account['status'] = '已登录'
                self.update_account_table()
                self.log(f"账号 {account.get('account', '')} 重新登录成功")
                return True
            else:
                # 登录失败，更新状态
                account['status'] = '登录失败'
                self.update_account_table()
                self.log(f"账号 {account.get('account', '')} 重新登录失败")
                return False
        return None  # 不是会话过期错误，返回None表示无需处理

    def update_account_status(self, account_name, running_status):
        """更新指定账号的运行状态"""
        for account in self.account_pool:
            if account.get('account') == account_name:
                account['running_status'] = running_status
                break
        self.update_account_table()

    def log(self, message, to_file=False):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        entry = f"【{timestamp}】>> {message}"
        
        # 如果to_file=True，只写入文件，不显示在UI
        if to_file:
            try:
                with open(self.log_filename, 'a', encoding='utf-8') as f:
                    f.write(entry + '\n')
            except Exception as e:
                print(f"写入日志文件失败: {e}")
        else:
            # 只有to_file=False时才显示在UI日志框
            if hasattr(self, 'log_text') and self.log_text:
                self.log_text.append(entry)
                if self.log_text.document().blockCount() > 1000:
                    self.log_text.clear()
                    self.log_text.append("日志过多，自动清除旧记录")
    
    def clear_log(self):
        """清空日志内容"""
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.clear()
            self.log("日志已清空")
    
    def send_push_message(self, content, summary=None):
        """推送消息到WxPusher"""
        url = "https://wxpusher.zjiecode.com/api/send/message"
        headers = {"Content-Type": "application/json"}
        app_token = "AT_yTDEgDCdt4olq1fY0CpZJipF5zhIPYCH"  # TODO: 替换为你的AppToken
        uids = [uid.strip() for uid in re.split(r'[，,;；\s]+', self.push_uid_input.text()) if uid.strip()]
        data = {
            "appToken": app_token,
            "content": content,
            "summary": summary or content[:20],
            "contentType": 2,
            "uids": uids,
            "url": "https://wxpusher.zjiecode.com",
            "verifyPayType": 0
        }
        try:
            resp = requests.post(url, headers=headers, data=json.dumps(data), timeout=10, verify=False)
            if resp.status_code == 200:
                self.log(f"推送成功: {content}")
            else:
                self.log(f"推送失败: {resp.text}")
        except Exception as e:
            self.log(f"推送异常: {e}")
    def test_wx_push(self):
        content = "<b style='color:#4CAF50;font-size:18px;'>推送测试</b><br>这是一条WxPusher推送测试消息。"
        self.send_push_message(content, "推送测试")
    def send_mail_push(self, subject, content):
        mail_host = self.mail_smtp_input.text().strip()
        try:
            mail_port = int(self.mail_port_input.text().strip() or 465)
        except Exception:
            mail_port = 465
        mail_user = self.mail_from_input.text().strip()
        mail_pass = self.mail_pass_input.text().strip()
        receivers = [x.strip() for x in re.split(r'[，,;；\s]+', self.mail_to_input.text()) if x.strip()]
        if not (mail_host and mail_user and mail_pass and receivers):
            self.log("邮件推送参数不完整")
            return
        msg = MIMEText(content, 'html', 'utf-8')
        msg['From'] = mail_user
        msg['To'] = ",".join(receivers)
        msg['Subject'] = Header(subject, 'utf-8')
        try:
            server = smtplib.SMTP_SSL(mail_host, mail_port)
            server.login(mail_user, mail_pass)
            server.sendmail(mail_user, receivers, msg.as_string())
            server.quit()
            self.log(f"邮件推送成功: {subject}")
        except Exception as e:
            self.log(f"邮件推送失败: {e}")
    def test_mail_push(self):
        content = "<b style='color:#4CAF50;font-size:18px;'>推送测试</b><br>这是一条邮件推送测试消息。"
        self.send_mail_push("推送测试", content)

    def edit_uid_list(self):
        # 弹窗编辑UID
        current = self.push_uid_input.text()
        lines = [x for x in re.split(r'[，,;；\s]+', current) if x.strip()]
        dlg = EditListDialog("编辑推送UID", '\n'.join(lines), self)
        if dlg.exec() == QDialog.DialogCode.Accepted:
            new_list = dlg.get_lines()
            self.push_uid_input.setText(','.join(new_list))
    def edit_mail_list(self):
        # 弹窗编辑邮箱
        current = self.mail_to_input.text()
        lines = [x for x in re.split(r'[，,;；\s]+', current) if x.strip()]
        dlg = EditListDialog("编辑收件人邮箱", '\n'.join(lines), self)
        if dlg.exec() == QDialog.DialogCode.Accepted:
            new_list = dlg.get_lines()
            self.mail_to_input.setText(','.join(new_list))

    # 代理账号登录，返回登录信息（含session）或None
    def admin_login(self, account_info):
        import re
        login_type = account_info.get('login_type')
        selected_domain = account_info.get('domain', '').strip()
        username = account_info.get('account', '').strip()
        password = account_info.get('password', '').strip()
        security_code = account_info.get('security_code', '').strip() if login_type == "登2" else ""
        if not selected_domain or not username or not password:
            self.log("登录失败：域名、账号或密码为空")
            return None
        domain = selected_domain if selected_domain.startswith("http") else f"https://{selected_domain}"
        ver = getattr(self, "global_ver", None)
        if not ver:
            # 自动获取Ver
            self.log("检测到Ver为空，正在自动获取...")
            ver = self.domain_get_ver_direct(domain)
            if ver:
                self.global_ver = ver
                self.log(f"已自动获取Ver: {ver}")
            else:
                self.log("自动获取Ver失败")
                return None
        login_url = f"{domain}/transform.php"
        query_params = {"ver": ver}
        login_layer = "ag" if login_type == "登3" else "su"
        pwd_safe = security_code if login_type == "登2" else "none"
        payload = (
            f"p=login_chk&ver={ver}&login_layer={login_layer}"
            f"&username={username}&pwd={password}&pwd_safe={pwd_safe}&uid=&blackbox="
        )
        headers = self.get_headers(domain)
        session = requests.Session()
        
        # 使用封装的请求函数
        resp = self.make_request(
            session=session,
            method="POST",
            url=login_url,
            params=query_params,
            data=payload,
            headers=headers,
            timeout=5,
            description=f"账号登录 - {username}"
        )
        
        if not resp:
            return None
            
        try:
            json_data = resp.json()
            if str(json_data.get("code")) == "201":
                self.log("本次登录需要验证码，请手动在网页完成登录")
                return None
            if json_data.get("status") == "success":
                self.log(f"账号 {username} 登录成功")
                return {
                    "session": session,
                    "uid": json_data.get("uid"),
                    "ver": ver,
                    "layer_id": json_data.get('layer_id', '')
                }
            else:
                self.log(f"代理登录失败: {json_data.get('status')}")
                return None
        except Exception as e:
            self.log(f"登录请求失败或解析失败: {e}")
            return None

    # 静默登录方法，专门用于后台重新登录，不输出任何日志
    def silent_admin_login(self, account_info):
        import re
        login_type = account_info.get('login_type')
        selected_domain = account_info.get('domain', '').strip()
        username = account_info.get('account', '').strip()
        password = account_info.get('password', '').strip()
        security_code = account_info.get('security_code', '').strip() if login_type == "登2" else ""
        
        if not selected_domain or not username or not password:
            return None
            
        domain = selected_domain if selected_domain.startswith("http") else f"https://{selected_domain}"
        ver = getattr(self, "global_ver", None)
        
        if not ver:
            # 静默获取Ver
            ver = self.silent_domain_get_ver_direct(domain)
            if ver:
                self.global_ver = ver
            else:
                return None
                
        login_url = f"{domain}/transform.php"
        query_params = {"ver": ver}
        login_layer = "ag" if login_type == "登3" else "su"
        pwd_safe = security_code if login_type == "登2" else "none"
        payload = (
            f"p=login_chk&ver={ver}&login_layer={login_layer}"
            f"&username={username}&pwd={password}&pwd_safe={pwd_safe}&uid=&blackbox="
        )
        headers = self.get_headers(domain)
        session = requests.Session()
        
        try:
            # 直接使用requests，不使用make_request（避免日志输出）
            resp = session.post(
                login_url,
                params=query_params,
                data=payload,
                headers=headers,
                timeout=5,
                verify=False
            )
            
            if not resp or resp.status_code != 200:
                return None
                
            json_data = resp.json()
            if str(json_data.get("code")) == "201":
                return None  # 需要验证码
                
            if json_data.get("status") == "success":
                return {
                    "session": session,
                    "uid": json_data.get("uid"),
                    "ver": ver,
                    "layer_id": json_data.get('layer_id', '')
                }
            else:
                return None
                
        except Exception:
            return None

    # 构建标准请求头，domain为完整域名（带http/https），cookies为dict或str
    def get_headers(self, domain, cookies=None, referer=None, origin=None):
        import re
        host = re.sub(r'^https?://', '', domain).split('/')[0]
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "*/*",
            "Content-Type": "application/x-www-form-urlencoded",
            "Host": host,
            "Origin": origin or domain,
            "Referer": referer or (domain + "/"),
            "Cookie": cookies or ""
        }
        if cookies:
            if isinstance(cookies, dict):
                cookie_str = "; ".join([f"{k}={v}" for k, v in cookies.items()])
            else:
                cookie_str = str(cookies)
            headers["Cookie"] = cookie_str
        return headers

    def make_request(self, session=None, method="GET", url="", params=None, data=None, headers=None, timeout=5, description=""):
        """
        封装的requests请求函数，统一处理请求和日志
        :param session: requests.Session对象，如果为None则使用requests直接请求
        :param method: 请求方法 GET/POST
        :param url: 请求URL
        :param params: URL参数
        :param data: POST数据
        :param headers: 请求头
        :param timeout: 超时时间
        :param description: 请求描述，用于日志
        :return: response对象或None
        """
        try:
            # 记录请求信息到文件（不显示在UI）
            self.log(f"【请求URL】{url}", to_file=True)
            if description:
                self.log(f"【请求描述】{description}", to_file=True)
            if params:
                self.log(f"【URL参数】{params}", to_file=True)
            if data:
                # 如果data太长，只显示前200个字符
                data_str = str(data)
                self.log(f"【POST数据】{data_str}", to_file=True)
            self.log(f"【请求头】{headers}", to_file=True)
            # 发送请求
            if session:
                if method.upper() == "POST":
                    response = session.post(url, params=params, data=data, headers=headers, timeout=timeout, verify=False)
                else:
                    response = session.get(url, params=params, headers=headers, timeout=timeout, verify=False)
            else:
                if method.upper() == "POST":
                    response = requests.post(url, params=params, data=data, headers=headers, timeout=timeout, verify=False)
                else:
                    response = requests.get(url, params=params, headers=headers, timeout=timeout, verify=False)
            
            # 记录响应信息到文件（不显示在UI）
            self.log(f"【响应状态】{response.status_code}", to_file=True)
            if response.status_code == 200:
                try:
                    # 尝试解析JSON响应
                    json_data = response.json()
                    self.log(f"【响应内容】{json_data}", to_file=True)
                except:
                    # 如果不是JSON，显示文本长度
                    self.log(f"【响应长度】{len(response.text)} 字符", to_file=True)
            else:
                self.log(f"【响应错误】{response.text[:200]}", to_file=True)
            
            # 写入空行到文件分隔
            self.write_log_file("")
            
            return response
            
        except Exception as e:
            self.log(f"【请求异常】{e}", to_file=True)
            # 写入空行到文件分隔
            self.write_log_file("")
            return None

    def write_log_file(self, message):
        """直接写入日志文件，不显示时间戳"""
        try:
            with open(self.log_filename, 'a', encoding='utf-8') as f:
                f.write(message + '\n')
        except Exception as e:
            print(f"写入日志文件失败: {e}")

    # 载入远程域名列表并填充到域名列表Tab
    def load_agent_domains(self):
        url = "http://0088-vip.com/"
        result = []
        try:
            response = requests.get(url, timeout=10, verify=False)
            response.raise_for_status()
            html_content = response.text
        except Exception as e:
            self.log(f"无法访问域名列表: {e}")
            return
        soup = BeautifulSoup(html_content, "html.parser")
        manage_section = soup.find("div", {"id": "list2"})
        if manage_section:
            manage_links = manage_section.find_all("a")
            for link in manage_links:
                title = link.get("title")
                if title:
                    domain = re.sub(r"^https?://", "", title)
                    if not re.match(r"^\d+\.\d+\.\d+\.\d+$", domain):
                        result.append(title)
        # 去重
        result = list(dict.fromkeys(result))
        
        # 存储域名列表，方便添加账号弹窗使用
        self.domain_list = [{"domain": domain} for domain in result]
        
        self.update_domain_list_table(result)

    # 更新域名列表Tab的表格内容
    def update_domain_list_table(self, domains):
        self.domain_list_table.setRowCount(len(domains))
        for i, d in enumerate(domains):
            self.domain_list_table.setItem(i, 0, QTableWidgetItem(d))
            msg_item = QTableWidgetItem("")
            self.domain_list_table.setItem(i, 1, msg_item)
            op_widget = QWidget()
            op_layout = QHBoxLayout(op_widget)
            op_layout.setContentsMargins(0, 0, 0, 0)
            btn_ping = QPushButton("检测延迟")
            btn_ping.setMaximumWidth(80)
            btn_ver = QPushButton("获取Ver")
            btn_ver.setMaximumWidth(80)
            btn_ping.clicked.connect(lambda _, row=i: self.domain_ping(row))
            btn_ver.clicked.connect(lambda _, row=i: self.domain_get_ver(row))
            op_layout.addWidget(btn_ping)
            op_layout.addWidget(btn_ver)
            op_layout.addStretch()
            self.domain_list_table.setCellWidget(i, 2, op_widget)
        self.log(f"已载入 {len(domains)} 个域名列表")

    # 检测指定域名的延迟并显示到消息列
    def domain_ping(self, row):
        domain = self.domain_list_table.item(row, 0).text()
        import time
        url = domain if domain.startswith('http') else f'https://{domain}'
        t1 = time.time()
        try:
            resp = requests.get(url, timeout=5, verify=False)
            t2 = time.time()
            msg = f"延迟: {int((t2-t1)*1000)}ms 状态: {resp.status_code}"
        except Exception as e:
            msg = f"请求失败: {e}"
        self.domain_list_table.setItem(row, 1, QTableWidgetItem(msg))

    # 获取指定域名首页的ver信息并显示到消息列，同时赋值给self.global_ver
    def domain_get_ver(self, row):
        domain = self.domain_list_table.item(row, 0).text()
        ver = self.domain_get_ver_direct(domain)
        if ver:
            msg = f"ver: {ver}"
            self.global_ver = ver
        else:
            msg = "未找到ver"
        self.domain_list_table.setItem(row, 1, QTableWidgetItem(msg))

    # 直接从指定域名获取Ver信息，返回Ver字符串或None
    def domain_get_ver_direct(self, domain):
        import re
        url = domain if domain.startswith('http') else f'https://{domain}'
        try:
            resp = requests.get(url, timeout=5, verify=False)
            html = resp.text
            # 更宽松的正则，允许后面有分号和空格
            ver_match = re.search(r"top\.ver\s*=\s*'([^']*)'", html, re.IGNORECASE)
            if ver_match:
                return ver_match.group(1)
            else:
                return None
        except Exception as e:
            self.log(f"获取Ver失败: {e}")
            return None

    # 静默获取Ver，不输出日志
    def silent_domain_get_ver_direct(self, domain):
        import re
        url = domain if domain.startswith('http') else f'https://{domain}'
        try:
            resp = requests.get(url, timeout=5, verify=False)
            html = resp.text
            # 更宽松的正则，允许后面有分号和空格
            ver_match = re.search(r"top\.ver\s*=\s*'([^']*)'", html, re.IGNORECASE)
            if ver_match:
                return ver_match.group(1)
            else:
                return None
        except Exception:
            return None

    def select_member_dialog(self, domain, admin_info, login_type):
        dlg = MemberSelectDialog(self, domain, admin_info, login_type)
        if dlg.exec() == QDialog.DialogCode.Accepted:
            return dlg.get_selected_ids()
        return None

    def closeEvent(self, event):
        """应用程序关闭事件"""
        # 总是显示确认对话框
        if hasattr(self, 'is_service_running') and self.is_service_running:
            # 如果服务正在运行
            reply = QMessageBox.question(
                self, '确认退出', 
                '监控服务正在运行，是否要停止服务并退出？\n退出前会自动保存配置。',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
        else:
            # 如果服务未运行
            reply = QMessageBox.question(
                self, '确认退出', 
                '确定要退出程序吗？\n退出前会自动保存配置。',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 如果服务正在运行，先停止服务
            if hasattr(self, 'is_service_running') and self.is_service_running:
                # 快速停止所有线程
                if hasattr(self, 'fetcher_threads'):
                    for fetcher in self.fetcher_threads.values():
                        fetcher.stop()
                
                # 停止HttpServer
                self.stop_http_server()
                
                # 等待最多2秒让线程停止
                import time
                start_time = time.time()
                while hasattr(self, 'fetcher_threads') and self.fetcher_threads and (time.time() - start_time) < 2:
                    QApplication.processEvents()
                    time.sleep(0.1)
            
            # 保存配置
            self.save_config()
            event.accept()
        else:
            event.ignore()

    def save_config(self):
        """保存配置到文件"""
        config = configparser.ConfigParser()
        
        # 基础设置
        config['DEFAULT'] = {
            'Port': self.port_input.text(),
            'Delay': self.delay_input.text(),
            'OnlyMonitor': str(self.only_monitor_checkbox.isChecked())
        }
        
        # WxPusher推送设置
        config['PUSH'] = {
            'enabled': str(self.push_enable_checkbox.isChecked()),
            'amount': self.push_amount_input.text(),
            'uid': self.push_uid_input.text()
        }
        
        # 邮件推送设置
        config['MAIL'] = {
            'enabled': str(self.mail_enable_checkbox.isChecked()),
            'from_email': self.mail_from_input.text(),
            'smtp_server': self.mail_smtp_input.text(),
            'smtp_port': self.mail_port_input.text(),
            'password': self.mail_pass_input.text(),
            'to_emails': self.mail_to_input.text()
        }
        
        # 账号池设置（排除登录状态和会话信息）
        account_pool_clean = []
        for account in self.account_pool:
            clean_account = {
                'login_type': account.get('login_type', ''),
                'domain': account.get('domain', ''),
                'account': account.get('account', ''),
                'password': account.get('password', ''),
                'security_code': account.get('security_code', ''),
                'monitor': account.get('monitor', ''),  # 保留旧的monitor字段兼容性
                'monitor_members': account.get('monitor_members', []),  # 新的监控会员列表
                'status': '未登录'  # 重置为未登录状态
                # 不保存 admin_info 和 session 信息
            }
            account_pool_clean.append(clean_account)
        
        config['ACCOUNTS'] = {
            'pool': json.dumps(account_pool_clean, ensure_ascii=False)
        }
        
        try:
            with open('ServerConfig.ini', 'w', encoding='utf-8') as f:
                config.write(f)
            self.log("配置已保存到 ServerConfig.ini")
        except Exception as e:
            self.log(f"保存配置失败: {e}")

    def load_config(self):
        """从文件读取配置"""
        config = configparser.ConfigParser()
        try:
            if not config.read('ServerConfig.ini', encoding='utf-8'):
                self.log("未找到配置文件，使用默认设置")
                return
            
            # 读取基础设置
            if 'DEFAULT' in config:
                default_cfg = config['DEFAULT']
                self.port_input.setText(default_cfg.get('Port', '8080'))
                self.delay_input.setText(default_cfg.get('Delay', '1'))
                self.only_monitor_checkbox.setChecked(default_cfg.getboolean('OnlyMonitor', False))
            
            # 读取WxPusher推送设置
            if 'PUSH' in config:
                push_cfg = config['PUSH']
                self.push_enable_checkbox.setChecked(push_cfg.getboolean('enabled', False))
                self.push_amount_input.setText(push_cfg.get('amount', ''))
                self.push_uid_input.setText(push_cfg.get('uid', ''))
            
            # 读取邮件推送设置
            if 'MAIL' in config:
                mail_cfg = config['MAIL']
                self.mail_enable_checkbox.setChecked(mail_cfg.getboolean('enabled', False))
                self.mail_from_input.setText(mail_cfg.get('from_email', ''))
                self.mail_smtp_input.setText(mail_cfg.get('smtp_server', ''))
                self.mail_port_input.setText(mail_cfg.get('smtp_port', ''))
                self.mail_pass_input.setText(mail_cfg.get('password', ''))
                self.mail_to_input.setText(mail_cfg.get('to_emails', ''))
            
            # 读取账号池设置
            if 'ACCOUNTS' in config:
                pool_str = config['ACCOUNTS'].get('pool', '[]')
                try:
                    self.account_pool = json.loads(pool_str)
                    # 确保所有账号状态都是未登录
                    for account in self.account_pool:
                        account['status'] = '未登录'
                        # 移除可能存在的登录信息
                        account.pop('admin_info', None)
                        account.pop('session', None)
                    
                    self.update_account_table()
                    self.log(f"已从配置文件加载 {len(self.account_pool)} 个账号")
                except json.JSONDecodeError:
                    self.log("账号池配置解析失败")
            
            self.log("配置文件加载完成")
            
        except Exception as e:
            self.log(f"读取配置文件失败: {e}")

    def toggle_service(self):
        """启动或停止监控服务"""
        if not hasattr(self, 'is_service_running'):
            self.is_service_running = False
        
        if not self.is_service_running:
            if not self.account_pool:
                self.log("账号池为空，请先添加账号")
                return
            
            self.fetcher_threads = {}
            started = 0
            try:
                delay = float(self.delay_input.text())
            except Exception:
                delay = 1.0
            
            for acc in self.account_pool:
                if acc.get("status") != "已登录":
                    self.log(f"账号 {acc.get('account')} 未登录，跳过")
                    continue
                
                domain = acc.get("domain")
                login_type = acc.get("login_type", "登3")
                admin_info = acc.get("admin_info")
                monitor_members = acc.get("monitor_members", [])  # 获取监控会员列表
                
                if not (domain and admin_info):
                    self.log(f"账号 {acc.get('account')} 信息不完整，跳过")
                    continue
                if not monitor_members:
                    self.log(f"账号 {acc.get('account')} 未设置监控会员，跳过")
                    continue
                
                fetcher = OrderFetcher(
                    self, domain, admin_info, login_type, delay, 
                    self.domain_list, acc, monitor_members, self.global_ver
                )
                fetcher.data_received.connect(self.update_order_table)
                fetcher.log_signal.connect(self.log)
                fetcher.update_account_signal.connect(self.update_account_from_signal)
                fetcher.update_monitor_message.connect(self.update_account_monitor_message)
                fetcher.request_relogin.connect(self.handle_relogin_request)
                fetcher.start()
                
                self.fetcher_threads[acc.get("account")] = fetcher
                
                # 设置账号运行状态为"运行中"
                acc['running_status'] = '运行中'
                
                started += 1
            
            if started > 0:
                # 启动 HttpServer.exe（如果没有勾选仅监控）
                if not self.only_monitor_checkbox.isChecked():
                    self.start_http_server()
                
                # 记录服务开始时间
                self.start_time = datetime.now()
                
                self.is_service_running = True
                self.start_stop_button.setText("停止服务")
                
                # 立即更新表格显示运行状态
                self.update_account_table()
                
                self.log(f"监控服务已启动，共启动 {started} 个账号监控")
            else:
                self.log("没有可启动的账号")
        else:
            # 停止服务
            self.log("正在停止监控服务...")
            self.start_stop_button.setEnabled(False)  # 防止重复点击
            
            if hasattr(self, 'fetcher_threads'):
                # 先发送停止信号给所有线程
                for fetcher in self.fetcher_threads.values():
                    fetcher.stop()
                
                # 使用QTimer异步等待线程停止，避免UI卡顿
                self.stop_timer = QTimer()
                self.stop_timer.timeout.connect(self.check_threads_stopped)
                self.stop_timer.start(100)  # 每100ms检查一次
            else:
                self.complete_service_stop()

    def check_threads_stopped(self):
        """检查所有线程是否已停止"""
        if not hasattr(self, 'fetcher_threads'):
            self.stop_timer.stop()
            self.complete_service_stop()
            return
            
        active_threads = []
        for account in list(self.fetcher_threads.keys()):
            fetcher = self.fetcher_threads.get(account)
            if fetcher and fetcher.isRunning():
                active_threads.append(account)
                # 更新该账号的运行状态为"停止中"
                for acc in self.account_pool:
                    if acc.get('account') == account:
                        acc['running_status'] = '停止中'
                        break
            else:
                # 线程已停止，从字典中移除，设置运行状态为"未运行"
                self.fetcher_threads.pop(account, None)
                for acc in self.account_pool:
                    if acc.get('account') == account:
                        acc['running_status'] = '未运行'
                        break
        
        # 更新表格显示
        self.update_account_table()
        
        if not active_threads:
            # 所有线程都已停止
            self.stop_timer.stop()
            self.complete_service_stop()
        # 不再输出日志，状态已显示在表格中

    def complete_service_stop(self):
        """完成服务停止的收尾工作"""
        # 停止运行时间计时
        if self.start_time:
            self.accumulated_time += int((datetime.now() - self.start_time).total_seconds())
            self.start_time = None
        
        # 设置所有账号运行状态为"未运行"
        for account in self.account_pool:
            account['running_status'] = '未运行'
        
        # 清理监控线程字典
        if hasattr(self, 'fetcher_threads'):
            self.fetcher_threads.clear()
        
        # 清理重新登录线程
        if hasattr(self, 'relogin_threads'):
            # 创建线程列表的副本，避免迭代时字典大小改变
            threads_to_cleanup = list(self.relogin_threads.values())
            accounts_to_cleanup = list(self.relogin_threads.keys())
            
            for i, thread in enumerate(threads_to_cleanup):
                try:
                    account_name = accounts_to_cleanup[i]
                    if thread.isRunning():
                        # 先尝试取消操作
                        if hasattr(thread, 'cancel'):
                            thread.cancel()
                        # 等待短时间让线程自然结束
                        if not thread.wait(500):  # 等待500ms
                            thread.terminate()
                            thread.wait(1000)  # 等待最多1秒
                    thread.deleteLater()
                except Exception as e:
                    self.log(f"清理重新登录线程异常: {e}")
            
            self.relogin_threads.clear()
        
        # 停止HttpServer
        self.stop_http_server()
        
        self.is_service_running = False
        self.start_stop_button.setText("启动服务")
        self.start_stop_button.setEnabled(True)
        self.update_account_table()  # 更新表格显示运行状态
        self.log("监控服务已停止")

    def start_http_server(self):
        """启动 HttpServer.exe"""
        try:
            port = int(self.port_input.text())
        except ValueError:
            self.log("请输入有效端口号")
            return
        
        import subprocess
        import os
        script_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        exe_path = os.path.join(script_dir, 'HttpServer.exe')
        
        if not os.path.isfile(exe_path):
            self.log(f"未找到 HttpServer.exe: {exe_path}")
            self.http_process = None
        else:
            try:
                args = [exe_path, '-port', str(port)]
                self.http_process = subprocess.Popen(
                    args,
                    cwd=script_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    shell=False
                )
                self.log(f"已启动 HttpServer.exe，PID={self.http_process.pid}，端口: {port}")
            except Exception as e:
                self.log(f"启动 HttpServer.exe 失败: {e}")
                self.http_process = None

    def stop_http_server(self):
        """停止 HttpServer.exe"""
        if hasattr(self, 'http_process') and self.http_process:
            try:
                self.log("正在终止 HttpServer.exe 进程...")
                self.http_process.terminate()
                self.http_process.wait(timeout=5)
                self.http_process = None
                self.log("HttpServer.exe 进程已停止")
            except Exception as e:
                self.log(f"终止 HttpServer.exe 进程时出错: {e}")
                try:
                    self.http_process.kill()
                    self.http_process = None
                except Exception:
                    pass

    def update_order_table(self, new_data):
        """更新订单表格"""
        if not hasattr(self, 'order_data'):
            self.order_data = []
        
        # 合并新数据到 self.order_data，避免重复
        existing_ids = {x['Wid'] for x in self.order_data}
        filtered = [d for d in new_data if d.get('Wid') not in existing_ids]
        
        if filtered:
            self.order_data.extend(filtered)
            if len(self.order_data) > 2000:
                self.order_data = self.order_data[-2000:]
            
            current = self.order_table.rowCount()
            self.order_table.setRowCount(current + len(filtered))
            
            monitored_orders = []
            for i, data in enumerate(filtered, start=current):
                user_str = f"{data.get('MemberID','')}-{data.get('UserName','')}"
                agent_account = data.get('account', '') or data.get('代理账号', '')
                
                self.order_table.setItem(i, 0, QTableWidgetItem(str(data.get('Wid',''))))
                self.order_table.setItem(i, 1, QTableWidgetItem(data.get('MatchInfo','')))
                self.order_table.setItem(i, 2, QTableWidgetItem(data.get('MatchType','')))
                self.order_table.setItem(i, 3, QTableWidgetItem(data.get('PlayType','')))
                self.order_table.setItem(i, 4, QTableWidgetItem(data.get('Hdp','')))
                self.order_table.setItem(i, 5, QTableWidgetItem(str(data.get('OrderMoney',''))))
                self.order_table.setItem(i, 6, QTableWidgetItem(data.get('Score','')))
                self.order_table.setItem(i, 7, QTableWidgetItem(user_str))
                self.order_table.setItem(i, 8, QTableWidgetItem(agent_account))  # 第8列：代理账号
                
                # 如果是指定会员的订单，添加到监控列表并记录日志
                if data.get('is_monitored', False):
                    monitored_orders.append(data)
                    self.log(f"监控到会员 {data.get('MemberID')} 的新订单: {data.get('MatchInfo')} - {data.get('PlayType')} - {data.get('Hdp')} - {data.get('OrderMoney')} - {data.get('UserName')}")
                else:
                    self.log(f"其他会员 {data.get('MemberID')} 的订单: {data.get('MatchInfo')} - {data.get('PlayType')} - {data.get('Hdp')} - {data.get('OrderMoney')} - {data.get('UserName')}")
            
            # 只将指定会员的订单写入 Order.json（使用ANSI格式），并推送
            if monitored_orders:
                try:
                    order_file = 'Order.json'
                    try:
                        with open(order_file, 'r', encoding='ansi') as f:
                            all_orders = json.load(f)
                    except Exception:
                        all_orders = []
                    
                    all_orders.extend(monitored_orders)
                    with open(order_file, 'w', encoding='ansi') as f:
                        json.dump(all_orders, f, ensure_ascii=True, indent=4)
                    self.log(f"已将 {len(monitored_orders)} 个指定会员订单写入 Order.json")
                except Exception as e:
                    self.log(f"写入 Order.json 失败: {e}")
                
                # 推送逻辑
                self.handle_push_notifications(monitored_orders)

    def handle_push_notifications(self, monitored_orders):
        """处理推送通知"""
        try:
            push_enabled = self.push_enable_checkbox.isChecked()
            push_amount = float(self.push_amount_input.text() or 0)
            push_uid = self.push_uid_input.toPlainText()
        except Exception:
            push_enabled = False
            push_amount = 0
            push_uid = ''
        
        try:
            mail_enabled = self.mail_enable_checkbox.isChecked()
            mail_amount = float(self.mail_amount_input.text() or 0)
        except Exception:
            mail_enabled = False
            mail_amount = 0
        
        if (push_enabled and push_uid) or mail_enabled:
            for order in monitored_orders:
                try:
                    money = float(order.get('OrderMoney', 0))
                except Exception:
                    money = 0
                
                # WxPusher推送
                if push_enabled and push_uid and money > push_amount:
                    content = (
                        f"<b style='color:#2196F3;font-size:18px;'>大额订单提醒</b><br>"
                        f"<b>订单号：</b>{order.get('Wid','')}<br>"
                        f"<b>比赛：</b>{order.get('MatchInfo','')}<br>"
                        f"<b>类型：</b>{order.get('MatchType','')}<br>"
                        f"<b>玩法：</b>{order.get('PlayType','')}<br>"
                        f"<b>盘口：</b>{order.get('Hdp','')}<br>"
                        f"<b>金额：</b><span style='color:red;font-weight:bold;'>{order.get('OrderMoney','')}</span><br>"
                        f"<b>比分：</b>{order.get('Score','')}<br>"
                        f"<b>会员：</b>{order.get('MemberID','')}-{order.get('UserName','')}<br>"
                        f"<b>代理账号：</b>{order.get('account','') or order.get('代理账号','')}<br>"
                        f"<span style='color:#888;font-size:12px;'>监控时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>"
                    )
                    summary = f"{order.get('MatchInfo','')} {order.get('OrderMoney','')}"
                    self.send_push_message(content, summary)
                
                # 邮件推送
                if mail_enabled and money > mail_amount:
                    self.send_email_notification(order)

    def handle_retry_login(self, acc):
        """处理重新登录请求"""
        self.log(f"账号 {acc.get('account')} 需要重新登录")
        # 这里可以添加重新登录的逻辑
        
    def update_account_from_signal(self, update_info):
        """从信号接收到的更新信息更新账号池"""
        account_name = update_info.get("account")
        for i, acc in enumerate(self.account_pool):
            if acc.get("account") == account_name:
                # 更新登录信息和状态
                self.account_pool[i]["admin_info"] = update_info.get("admin_info")
                self.account_pool[i]["status"] = update_info.get("status")
                break
        self.update_account_table()
    
    def update_account_monitor_message(self, account_name, message):
        """更新账号的监控消息"""
        # 在账号表格中找到对应的行并更新监控消息列
        for row in range(self.account_table.rowCount()):
            if self.account_table.item(row, 1) and self.account_table.item(row, 1).text() == account_name:
                # 第4列是监控消息列
                monitor_item = QTableWidgetItem(message)
                monitor_item.setFlags(monitor_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 设置为不可编辑
                self.account_table.setItem(row, 4, monitor_item)
                break

    def send_email_notification(self, order):
        """发送邮件通知"""
        try:
            smtp_server = self.mail_smtp_input.text()
            smtp_port = int(self.mail_port_input.text() or 587)
            email_user = self.mail_from_input.text()
            email_pass = self.mail_pass_input.text()
            to_email = self.mail_to_input.text()
            
            if not all([smtp_server, email_user, email_pass, to_email]):
                self.log("邮件配置不完整，跳过邮件推送")
                return
            
            # 构造邮件内容
            subject = f"大额订单提醒 - {order.get('OrderMoney', '')}"
            content = f"""
订单号：{order.get('Wid', '')}
比赛：{order.get('MatchInfo', '')}
类型：{order.get('MatchType', '')}
玩法：{order.get('PlayType', '')}
盘口：{order.get('Hdp', '')}
金额：{order.get('OrderMoney', '')}
比分：{order.get('Score', '')}
会员：{order.get('MemberID', '')}-{order.get('UserName', '')}
代理账号：{order.get('account', '') or order.get('代理账号', '')}
监控时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            msg = MIMEText(content, 'plain', 'utf-8')
            msg['From'] = email_user
            msg['To'] = to_email
            msg['Subject'] = Header(subject, 'utf-8')
            
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(email_user, email_pass)
                server.send_message(msg)
            
            self.log(f"邮件推送成功: {order.get('MatchInfo', '')} - {order.get('OrderMoney', '')}")
        except Exception as e:
            self.log(f"邮件推送失败: {e}")

    def update_runtime_display(self):
        """更新状态栏运行时间显示"""
        total_seconds = self.accumulated_time
        if self.start_time:
            # 如果正在运行，加上当前运行时间
            total_seconds += int((datetime.now() - self.start_time).total_seconds())
        
        days = total_seconds // 86400
        hours = (total_seconds % 86400) // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        self.runtime_label.setText(f"运行时间: {days}天{hours}小时{minutes}分{seconds}秒")

    def handle_relogin_request(self, response_data, account_info):
        """处理来自OrderFetcher的重新登录请求"""
        account_name = account_info.get('account', '')
        
        try:
            # 检查是否已经有该账号的重新登录线程在运行
            if (hasattr(self, 'relogin_threads') and 
                account_name in self.relogin_threads and 
                self.relogin_threads[account_name].isRunning()):
                return  # 已有线程在处理，忽略重复请求
            
            # 更新表格状态显示为"重新登录中"
            for account in self.account_pool:
                if account.get('account') == account_name:
                    account['status'] = '重新登录中'
                    break
            self.update_account_table()
            
            # 创建后台重新登录线程
            relogin_thread = ReloginThread(self, response_data, account_info)
            relogin_thread.relogin_completed.connect(self.on_relogin_completed, Qt.ConnectionType.QueuedConnection)
            relogin_thread.finished.connect(lambda: self.cleanup_relogin_thread(account_name), Qt.ConnectionType.QueuedConnection)
            
            # 保存线程引用并启动
            if not hasattr(self, 'relogin_threads'):
                self.relogin_threads = {}
            self.relogin_threads[account_name] = relogin_thread
            relogin_thread.start()
            
            # 添加超时处理，防止线程无限期运行
            def timeout_handler():
                try:
                    if (account_name in self.relogin_threads and 
                        self.relogin_threads[account_name].isRunning()):
                        thread = self.relogin_threads[account_name]
                        thread.cancel()
                        if not thread.wait(1000):
                            thread.terminate()
                        self.cleanup_relogin_thread(account_name)
                        # 重置OrderFetcher状态
                        if hasattr(self, 'fetcher_threads') and account_name in self.fetcher_threads:
                            fetcher = self.fetcher_threads[account_name]
                            fetcher.is_relogin_in_progress = False
                        # 更新表格状态为超时
                        for account in self.account_pool:
                            if account.get('account') == account_name:
                                account['status'] = '登录超时'
                                break
                        self.update_account_table()
                except Exception:
                    pass
            
            # 30秒后执行超时处理
            QTimer.singleShot(30000, timeout_handler)
        except Exception:
            # 如果创建线程失败，重置OrderFetcher状态
            if hasattr(self, 'fetcher_threads') and account_name in self.fetcher_threads:
                fetcher = self.fetcher_threads[account_name]
                fetcher.is_relogin_in_progress = False
    
    def cleanup_relogin_thread(self, account_name):
        """清理已完成的重新登录线程"""
        if hasattr(self, 'relogin_threads') and account_name in self.relogin_threads:
            thread = self.relogin_threads[account_name]
            thread.deleteLater()
            # 安全地删除字典项
            try:
                del self.relogin_threads[account_name]
            except KeyError:
                pass  # 如果已经被删除，忽略错误
    
    def on_relogin_completed(self, account_name, success, new_admin_info):
        """处理重新登录完成"""
        try:
            if success:
                # 更新账号池中的状态和登录信息
                for account in self.account_pool:
                    if account.get('account') == account_name:
                        account['admin_info'] = new_admin_info
                        account['status'] = '已登录'
                        break
                
                # 更新账号表格
                self.update_account_table()
                
                # 通知对应的fetcher更新admin_info并重置状态
                if hasattr(self, 'fetcher_threads') and account_name in self.fetcher_threads:
                    fetcher = self.fetcher_threads[account_name]
                    fetcher.admin_info = new_admin_info
                    fetcher.login_retry_count = 0  # 重置重试计数器
                    fetcher.is_relogin_in_progress = False  # 重置重新登录状态
                    fetcher.update_monitor_message.emit(account_name, "监控中...")
            else:
                # 更新账号池中的状态为登录失败
                for account in self.account_pool:
                    if account.get('account') == account_name:
                        account['status'] = '登录失败'
                        break
                
                # 更新账号表格
                self.update_account_table()
                
                # 通知对应的fetcher处理失败情况并重置状态
                if hasattr(self, 'fetcher_threads') and account_name in self.fetcher_threads:
                    fetcher = self.fetcher_threads[account_name]
                    fetcher.is_relogin_in_progress = False  # 重置重新登录状态
                    if fetcher.login_retry_count >= fetcher.max_login_retries:
                        fetcher.update_monitor_message.emit(account_name, "登录失败，暂停中...")
                        fetcher.login_retry_count = 0  # 重置计数器
                    else:
                        fetcher.update_monitor_message.emit(account_name, f"登录失败，重试中({fetcher.login_retry_count}/{fetcher.max_login_retries})...")
        except Exception:
            # 确保重置状态
            try:
                if hasattr(self, 'fetcher_threads') and account_name in self.fetcher_threads:
                    fetcher = self.fetcher_threads[account_name]
                    fetcher.is_relogin_in_progress = False
            except Exception:
                pass

    def batch_login_accounts(self):
        """一键登录所有账号"""
        if not self.account_pool:
            QMessageBox.information(self, "提示", "没有可登录的账号")
            return
        
        # 确认操作
        reply = QMessageBox.question(
            self, 
            "确认操作", 
            f"确定要登录所有 {len(self.account_pool)} 个账号吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        self.log(f"开始一键登录，共 {len(self.account_pool)} 个账号")
        
        # 逐个登录账号
        success_count = 0
        for i, account in enumerate(self.account_pool):
            try:
                # 调用单个账号登录逻辑
                result = self.login_single_account(account)
                if result:
                    success_count += 1
                    account['status'] = '已登录'
                else:
                    account['status'] = '登录失败'
                
                # 更新表格显示
                self.update_account_table()
                
                # 短暂延迟，避免请求过于频繁
                QApplication.processEvents()
                
            except Exception as e:
                self.log(f"账号 {account.get('account', '')} 登录异常: {str(e)}")
                account['status'] = '登录失败'
        
        # 最终结果
        self.log(f"一键登录完成，成功: {success_count}/{len(self.account_pool)} 个账号")
        QMessageBox.information(self, "登录完成", f"登录完成\n成功: {success_count} 个\n失败: {len(self.account_pool) - success_count} 个")

    
    def login_single_account(self, account_data):
        """登录单个账号，返回是否成功"""
        try:
            domain = account_data.get('domain', '')
            account = account_data.get('account', '')
            password = account_data.get('password', '')
            login_type = account_data.get('login_type', '登1')
            
            if not all([domain, account, password]):
                self.log(f"账号 {account} 信息不完整，跳过登录")
                return False
            
            # 如果全局Ver为空，先获取Ver
            if not self.global_ver:
                self.log("检测到Ver为空，正在自动获取...")
                self.domain_get_ver_direct(domain)
                if self.global_ver:
                    self.log(f"已自动获取Ver: {self.global_ver}")
            
            # 执行登录 - 传递完整的account_data
            result = self.admin_login(account_data)
            if result and result.get('session'):
                # 保存登录信息到账号数据
                account_data['admin_info'] = result
                self.log(f"账号 {account} 登录成功")
                return True
            else:
                self.log(f"账号 {account} 登录失败")
                return False
                
        except Exception as e:
            self.log(f"账号 {account_data.get('account', '')} 登录异常: {str(e)}")
            return False

# 重新登录线程，避免阻塞主线程
class ReloginThread(QThread):
    relogin_completed = pyqtSignal(str, bool, dict)  # 账号名, 成功状态, 新的admin_info
    
    def __init__(self, parent, response_data, account_info):
        super().__init__(parent)
        self.response_data = response_data
        self.account_info = account_info.copy()  # 创建副本，避免并发修改
        self.is_cancelled = False
    
    def cancel(self):
        """取消重新登录操作"""
        self.is_cancelled = True
    
    def run(self):
        """在后台线程中执行重新登录"""
        account_name = self.account_info.get('account', '')
        try:
            if self.is_cancelled:
                return
            
            # 使用静默登录方法，避免向主线程发送日志信号
            new_admin_info = self.parent().silent_admin_login(self.account_info)
            
            if self.is_cancelled:
                return
            
            if new_admin_info:
                # 更新账号池中的登录信息
                self.account_info['admin_info'] = new_admin_info
                self.account_info['status'] = '已登录'
                self.relogin_completed.emit(account_name, True, new_admin_info)
            else:
                # 登录失败
                self.account_info['status'] = '登录失败'
                self.relogin_completed.emit(account_name, False, {})
        except Exception as e:
            try:
                self.relogin_completed.emit(account_name, False, {})
            except Exception:
                pass  # 忽略信号发送失败
        finally:
            # 确保线程正确结束
            try:
                self.quit()
            except Exception:
                pass

if __name__ == '__main__':
    import sys
    import traceback
    
    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录异常到日志
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logging.error(f"未捕获的异常: {error_msg}")
        print(f"未捕获的异常: {error_msg}")
    
    # 设置全局异常处理器
    sys.excepthook = handle_exception
    
    app = QApplication(sys.argv)
    try:
        window = CrawlerWindow()
        window.show()
        sys.exit(app.exec())
    except Exception as e:
        logging.error(f"应用程序启动异常: {e}")
        print(f"应用程序启动异常: {e}")
        sys.exit(1)