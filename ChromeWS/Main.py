import subprocess
import time
import json
import requests
import websocket
import threading
import execjs
import os
import base64
from urllib.parse import urlparse


def load_js_file(js_file_path):
    """加载JavaScript文件"""
    try:
        # 获取脚本所在目录的绝对路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # 构建完整的文件路径
        full_path = os.path.join(script_dir, js_file_path)

        print(f"尝试加载JS文件: {full_path}")

        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # print(f"成功加载JS文件，大小: {len(content)} 字符")
            return content
    except Exception as e:
        print(f"加载JS文件失败: {e}")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
        return None


def decrypt_data(encrypted_data, js_file_path, function_name):
    """使用JavaScript函数解密数据"""
    try:
        js_code = load_js_file(js_file_path)
        if js_code is None:
            return f"JS文件加载失败"

        ctx = execjs.compile(js_code)
        decrypted_data = ctx.call(function_name, encrypted_data)

        # 如果解密结果是空字符串，可能表示解密失败
        # if decrypted_data == "":
        #     return f"JavaScript解密返回空字符串"

        # print(f"JavaScript解密成功，结果长度: {len(str(decrypted_data))}")
        # print(f"解密结果前100字符: {str(decrypted_data)[:100]}")

        return decrypted_data
    except Exception as e:
        return f"JavaScript解密失败: {str(e)}"


class AESDecryptor:
    """使用JavaScript进行AES解密"""

    def __init__(self):
        self.js_file_path = 'Aes.js'
        self.function_name = 'Decrypt'

    def decrypt(self, encrypted_data):
        """使用JavaScript解密数据"""
        return decrypt_data(encrypted_data, self.js_file_path, self.function_name)

    def try_decrypt_json(self, encrypted_data):
        """尝试解密并解析为JSON"""
        decrypted = self.decrypt(encrypted_data)
        if not str(decrypted).startswith("JS文件加载失败") and not str(decrypted).startswith("JavaScript解密失败"):
            try:
                # 如果解密后的结果是JSON字符串，尝试解析
                if isinstance(decrypted, str) and (
                        decrypted.strip().startswith('{') or decrypted.strip().startswith('[')):
                    return json.loads(decrypted)
                else:
                    return decrypted
            except json.JSONDecodeError:
                return decrypted
        return decrypted


class ChromeWebSocketMonitor:
    def __init__(self, chrome_path, target_url, debug_port=9222):
        self.chrome_path = chrome_path
        self.target_url = target_url
        self.debug_port = debug_port
        self.chrome_process = None
        self.ws_connection = None
        self.tab_id = None

        # 初始化AES解密器
        self.decryptor = AESDecryptor()

    def start_chrome_with_debugging(self):
        """启动Chrome浏览器并启用远程调试"""
        print(f"启动Chrome浏览器，调试端口: {self.debug_port}")

        # Chrome启动参数
        chrome_args = [
            self.chrome_path,
            f"--remote-debugging-port={self.debug_port}",
            "--remote-allow-origins=*",  # 允许所有来源的远程调试连接
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--disable-extensions",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps",
            "--disable-sync",
            f"--user-data-dir=C:\\temp\\chrome_debug_profile",
            self.target_url
        ]

        try:
            self.chrome_process = subprocess.Popen(chrome_args)
            print("Chrome浏览器启动成功")
            time.sleep(3)  # 等待Chrome完全启动
            return True
        except Exception as e:
            print(f"启动Chrome失败: {e}")
            return False

    def get_tabs(self):
        """获取Chrome标签页列表"""
        try:
            response = requests.get(f"http://localhost:{self.debug_port}/json")
            tabs = response.json()
            return tabs
        except Exception as e:
            print(f"获取标签页失败: {e}")
            return []

    def find_target_tab(self):
        """查找目标网页的标签页"""
        tabs = self.get_tabs()
        target_domain = "599.com"  # 匹配域名

        for tab in tabs:
            tab_url = tab.get('url', '')
            # 检查是否包含目标域名或完整URL
            if target_domain in tab_url or self.target_url in tab_url:
                return tab
        return None

    def connect_to_tab(self):
        """连接到目标标签页"""
        # 等待一段时间让页面完全加载
        max_retries = 10
        for attempt in range(max_retries):
            target_tab = self.find_target_tab()
            if target_tab:
                break
            print(f"等待目标标签页加载... ({attempt + 1}/{max_retries})")
            time.sleep(2)

        if not target_tab:
            print("未找到目标标签页，请检查网址是否正确")
            return False

        self.tab_id = target_tab['id']
        ws_url = target_tab['webSocketDebuggerUrl']

        print(f"连接到标签页: {target_tab.get('title', 'Unknown')}")
        print(f"标签页URL: {target_tab.get('url', 'Unknown')}")
        print(f"WebSocket URL: {ws_url}")

        try:
            self.ws_connection = websocket.WebSocketApp(
                ws_url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            return True
        except Exception as e:
            print(f"连接标签页失败: {e}")
            return False

    def on_open(self, ws):
        """WebSocket连接打开时的回调"""
        print("已连接到Chrome DevTools")

        # 启用网络域以监听WebSocket
        enable_network = {
            "id": 1,
            "method": "Network.enable"
        }
        ws.send(json.dumps(enable_network))

        # 启用运行时域
        enable_runtime = {
            "id": 2,
            "method": "Runtime.enable"
        }
        ws.send(json.dumps(enable_runtime))

        # 启用页面域
        enable_page = {
            "id": 3,
            "method": "Page.enable"
        }
        ws.send(json.dumps(enable_page))

    def on_message(self, ws, message):
        """处理从Chrome DevTools接收到的消息"""
        try:
            data = json.loads(message)
            method = data.get('method', '')

            # 监听WebSocket相关事件
            if method == 'Network.webSocketCreated':
                print(f"\n🔗 WebSocket连接创建:")
                print(f"   URL: {data['params']['url']}")
                print(f"   Request ID: {data['params']['requestId']}")

            elif method == 'Network.webSocketFrameReceived':
                frame_data = data['params']['response']
                payload = frame_data['payloadData']
                opcode = frame_data['opcode']

                print(f"\n📥 WebSocket接收消息:")
                print(f"   原始Payload: {payload}")
                print(f"   OpCode: {opcode}")

                # 处理WebSocket二进制数据
                try:
                    if opcode == 2:  # 二进制帧
                        # WebSocket二进制数据需要先Base64解码，然后UTF-8解码
                        import base64
                        binary_data = base64.b64decode(payload)
                        text_payload = binary_data.decode('utf-8')
                        # print(f"   二进制数据解码后: {text_payload[:100]}...")

                        # 使用解码后的文本进行AES解密
                        decrypted_data = self.decryptor.try_decrypt_json(text_payload)
                    else:
                        # 文本帧直接解密
                        decrypted_data = self.decryptor.try_decrypt_json(payload)

                    # 检查是否包含任何错误信息
                    error_keywords = ["JS文件加载失败", "JavaScript解密失败"]
                    has_error = any(str(decrypted_data).startswith(keyword) for keyword in error_keywords)

                    if not has_error:
                        print(f"   🔓 解密后数据: {decrypted_data}")
                        # 如果解密后是JSON格式，尝试美化输出
                        # if isinstance(decrypted_data, dict):
                        #     print(f"   📋 格式化JSON:")
                        #     print(json.dumps(decrypted_data, indent=2, ensure_ascii=False))
                    else:
                        print(f"   ❌ {decrypted_data}")

                except Exception as e:
                    print(f"   ❌ 处理WebSocket数据失败: {e}")
                    # 如果处理失败，尝试直接解密原始payload
                    decrypted_data = self.decryptor.try_decrypt_json(payload)
                    print(f"   直接解密结果: {decrypted_data}")


            elif method == 'Network.webSocketClosed':
                print(f"\n❌ WebSocket连接关闭:")
                print(f"   Request ID: {data['params']['requestId']}")

        except Exception as e:
            print(f"处理消息时出错: {e}")

    def on_error(self, ws, error):
        """WebSocket错误回调"""
        print(f"WebSocket错误: {error}")

    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket关闭回调"""
        print("与Chrome DevTools的连接已关闭")

    def start_monitoring(self):
        """开始监控"""
        if not self.start_chrome_with_debugging():
            return False

        if not self.connect_to_tab():
            return False

        print("\n开始监控WebSocket消息... (按Ctrl+C退出)")
        try:
            self.ws_connection.run_forever()
        except KeyboardInterrupt:
            print("\n停止监控...")
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        if self.ws_connection:
            self.ws_connection.close()

        if self.chrome_process:
            try:
                self.chrome_process.terminate()
                print("Chrome进程已终止")
            except:
                pass




def main():
    # 配置参数
    chrome_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe"
    target_url = "https://599.com/live/"
    debug_port = 9222

    # 创建监控器实例
    monitor = ChromeWebSocketMonitor(chrome_path, target_url, debug_port)

    # 开始监控
    monitor.start_monitoring()


if __name__ == "__main__":
    main() 