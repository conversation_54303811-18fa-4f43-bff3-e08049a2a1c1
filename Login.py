import sys
import json
import hashlib
import time
import uuid
import requests
import platform
import urllib3
import os
import configparser
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QMessageBox, QFrame, QDialog
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPalette, QBrush, QColor

# 导入泡椒云SDK
from PJYSDK import *

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class HeartbeatThread(QThread):
    """心跳线程"""
    heartbeat_failed = pyqtSignal(dict)
    
    def __init__(self, pjysdk):
        super().__init__()
        self.pjysdk = pjysdk
        self.running = True
        
    def run(self):
        while self.running:
            time.sleep(30)  # 每30秒检查一次心跳
            if self.running:
                result = self.pjysdk.get_heartbeat_result()
                if result['code'] != 0:
                    self.heartbeat_failed.emit(result)
                    break
    
    def stop(self):
        self.running = False
        self.quit()
        self.wait()


class PaojiaoAuth:
    """泡椒云网络验证类 - 使用官方SDK"""
    
    def __init__(self, app_key, app_secret):
        self.app_key = app_key
        self.app_secret = app_secret
        
        # 初始化SDK
        self.pjysdk = PJYSDK(app_key, app_secret)
        self.pjysdk.debug = True
        
        # 设置设备ID
        device_id = self.get_device_id()
        self.pjysdk.set_device_id(device_id)
        
        self.heartbeat_thread = None
        
    def get_device_id(self):
        """获取设备唯一标识"""
        try:
            # 使用多种系统信息生成设备ID
            system_info = f"{platform.system()}-{platform.machine()}-{platform.processor()}-{platform.node()}"
            device_id = hashlib.md5(system_info.encode()).hexdigest()[:32]
            return device_id
        except Exception as e:
            # 如果获取失败，使用时间戳作为备选
            return str(int(time.time()))
    
    def login(self, card):
        """卡密登录"""
        try:
            # 设置卡密
            self.pjysdk.set_card(card)
            
            # 执行登录
            result = self.pjysdk.card_login()
            
            if result['code'] == 0:
                # 启动心跳线程
                self.start_heartbeat_thread()
                return True, result
            else:
                return False, result
                
        except Exception as e:
            return False, {"code": -1, "message": f"登录异常: {str(e)}"}
    
    def trial_login(self):
        """试用登录"""
        try:
            result = self.pjysdk.trial_login()
            
            if result['code'] == 0:
                # 启动心跳线程
                self.start_heartbeat_thread()
                return True, result
            else:
                return False, result
                
        except Exception as e:
            return False, {"code": -1, "message": f"试用登录异常: {str(e)}"}
    
    def start_heartbeat_thread(self):
        """启动心跳线程"""
        if self.heartbeat_thread:
            self.heartbeat_thread.stop()
        
        self.heartbeat_thread = HeartbeatThread(self.pjysdk)
        self.heartbeat_thread.start()
    
    def heartbeat(self):
        """心跳保持"""
        try:
            return self.pjysdk.get_heartbeat_result()
        except Exception as e:
            return {"code": -1, "message": f"心跳异常: {str(e)}"}
    

    def get_notice(self):
        """获取软件公告"""
        try:
            result = self.pjysdk.get_software_notice()
            if result['code'] == 0:
                # 根据API返回结果，字段名是 notice
                return result['result']['notice']
            else:
                return "暂无公告"
        except Exception as e:
            return f"公告加载失败: {str(e)}"
    
    def get_login_info(self):
        """获取登录信息"""
        try:
            if hasattr(self.pjysdk, 'login_result'):
                login_info = self.pjysdk.login_result
                return {
                    'card_type': login_info.card_type,
                    'expires': login_info.expires,
                    'expires_ts': login_info.expires_ts,
                    'config': getattr(login_info, 'config', ''),
                    'is_trial': getattr(self.pjysdk, 'is_trial', False)
                }
            return None
        except:
            return None
    
    def get_time_remaining(self):
        """获取剩余时间（秒）"""
        try:
            return self.pjysdk.get_time_remaining()
        except:
            return 0
    
    def format_time_remaining(self):
        """格式化剩余时间"""
        try:
            remaining_seconds = self.get_time_remaining()
            
            if remaining_seconds <= 0:
                return "已过期"
            
            days = remaining_seconds // 86400
            hours = (remaining_seconds % 86400) // 3600
            minutes = (remaining_seconds % 3600) // 60
            
            if days > 0:
                return f"{days}天{hours}小时{minutes}分钟"
            elif hours > 0:
                return f"{hours}小时{minutes}分钟"
            else:
                return f"{minutes}分钟"
                
        except:
            return "未知"
    
    def logout(self):
        """退出登录"""
        try:
            if self.heartbeat_thread:
                self.heartbeat_thread.stop()
            
            if hasattr(self.pjysdk, 'is_trial') and self.pjysdk.is_trial:
                self.pjysdk.trial_logout()
            else:
                self.pjysdk.card_logout()
        except:
            pass


class LoginWindow(QMainWindow):
    """登录窗口"""
    
    # 配置您的泡椒云应用密钥
    APP_KEY = "d1u5ssjdqusq2phip1l0"  # 请替换为您的实际app_key
    APP_SECRET = "Vhe3VTNvCwawBq7Y4nzujueG4uGA8dZ8"  # 请替换为您的实际app_secret
    
    login_success = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        # 初始化验证系统
        self.auth = PaojiaoAuth(self.APP_KEY, self.APP_SECRET)
        
        # 设置心跳失败回调
        if self.auth.heartbeat_thread:
            self.auth.heartbeat_thread.heartbeat_failed.connect(self.on_heartbeat_failed)
        
        self.init_ui()
        self.load_notice()
        self.load_saved_card()  # 加载保存的卡密
        
    def on_heartbeat_failed(self, result):
        """心跳失败回调"""
        print(f"心跳失败: {result['message']}")
        
        if result['code'] == 10214:  # 登录过期
            QMessageBox.warning(self, "警告", "登录已过期，请重新登录")
            return
            
        # 可以在这里添加重连逻辑
        QMessageBox.warning(self, "连接异常", f"网络连接异常: {result['message']}")
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🔐 软件授权验证")
        self.setFixedSize(500, 600)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:1 #764ba2);
            }
            QWidget {
                color: #333;
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            QLabel {
                color: #2c3e50;
                font-size: 14px;
            }
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
            QPushButton {
                border: none;
                border-radius: 5px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
                color: white;
                min-height: 20px;
            }
            QPushButton:hover {
                opacity: 0.8;
            }
            QPushButton:pressed {
                opacity: 0.6;
            }
            .login-btn {
                background-color: #4CAF50;
            }
                         .trial-btn {
                 background-color: #2196F3;
             }
            .exit-btn {
                background-color: #f44336;
            }
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: white;
                font-size: 12px;
            }
            QFrame {
                background-color: white;
                border-radius: 10px;
            }
        """)
        
        main_layout = QVBoxLayout()
        main_widget = QWidget()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("🔐 软件授权验证")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2196F3; margin-bottom: 20px;")
        main_layout.addWidget(title_label)
        
        # 登录表单框
        login_frame = QFrame()
        login_frame.setStyleSheet("QFrame { padding: 20px; }")
        login_layout = QVBoxLayout(login_frame)
        
        # 卡密输入
        card_label = QLabel("🎫 请输入卡密:")
        card_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        self.card_input = QLineEdit()
        self.card_input.setPlaceholderText("请输入您的授权卡密...")
        self.card_input.returnPressed.connect(self.login)
        
        login_layout.addWidget(card_label)
        login_layout.addWidget(self.card_input)
        login_layout.addSpacing(10)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.login_btn = QPushButton("🔓 登录验证")
        self.login_btn.setProperty("class", "login-btn")
        self.login_btn.clicked.connect(self.login)
        
        self.trial_btn = QPushButton("🆓 试用登录")
        self.trial_btn.setProperty("class", "trial-btn")
        self.trial_btn.clicked.connect(self.trial_login)
        
        button_layout.addWidget(self.login_btn)
        button_layout.addWidget(self.trial_btn)
        
        login_layout.addLayout(button_layout)
        
        main_layout.addWidget(login_frame)
        
        # 公告显示框
        notice_label = QLabel("📢 软件公告")
        notice_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        
        self.notice_text = QTextEdit()
        self.notice_text.setReadOnly(True)
        self.notice_text.setMaximumHeight(150)
        self.notice_text.setPlaceholderText("正在加载公告...")
        
        main_layout.addWidget(notice_label)
        main_layout.addWidget(self.notice_text)
        
        # 底部退出按钮
        bottom_layout = QHBoxLayout()
        
        exit_btn = QPushButton("❌ 退出")
        exit_btn.setProperty("class", "exit-btn")
        exit_btn.setMaximumWidth(80)
        exit_btn.clicked.connect(self.close)
        
        bottom_layout.addStretch()
        bottom_layout.addWidget(exit_btn)
        
        main_layout.addLayout(bottom_layout)
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def login(self):
        """卡密登录"""
        card = self.card_input.text().strip()
        if not card:
            QMessageBox.warning(self, "警告", "请输入卡密")
            return
        
        self.login_btn.setText("验证中...")
        self.login_btn.setEnabled(False)
        QApplication.processEvents()
        
        success, result = self.auth.login(card)
        
        self.login_btn.setText("🔓 登录验证")
        self.login_btn.setEnabled(True)
        
        if success:
            # 登录成功，保存卡密
            self.save_card_config(card)
            
            login_info = self.auth.get_login_info()
            if login_info:
                success_msg = f"""登录成功！
                                卡密类型: {login_info['card_type']}
                                过期时间: {login_info['expires']}
                                剩余时间: {self.auth.format_time_remaining()}

                                设备已绑定，验证完成！
                                卡密已自动保存，下次启动将自动填充。"""
            else:
                success_msg = "登录验证成功！\n卡密已自动保存，下次启动将自动填充。"
            
            QMessageBox.information(self, "登录成功", success_msg)
            
            # 发射登录成功信号
            self.login_success.emit()
            
        else:
            # 登录失败
            QMessageBox.critical(self, "登录失败", f"错误代码: {result['code']}\n{result['message']}")
    
    def trial_login(self):
        """试用登录"""
        self.trial_btn.setText("试用中...")
        self.trial_btn.setEnabled(False)
        QApplication.processEvents()
        
        success, result = self.auth.trial_login()
        
        self.trial_btn.setText("🆓 试用登录")
        self.trial_btn.setEnabled(True)
        
        if success:
            # 试用登录成功，保存试用标记
            self.save_card_config("TRIAL_MODE")
            
            login_info = self.auth.get_login_info()
            if login_info:
                success_msg = f"""试用登录成功！

试用时间: {self.auth.format_time_remaining()}
过期时间: {login_info['expires']}

开始试用体验！
试用状态已保存，下次启动将记住试用模式。"""
            else:
                success_msg = "试用登录成功！\n试用状态已保存。"
            
            QMessageBox.information(self, "试用成功", success_msg)
            
            # 发射登录成功信号
            self.login_success.emit()
            
        else:
            QMessageBox.critical(self, "试用失败", f"错误代码: {result['code']}\n{result['message']}")
    
        
    def load_notice(self):
        """加载软件公告"""
        try:
            notice = self.auth.get_notice()
            # 处理换行符，确保正确显示
            if notice and isinstance(notice, str):
                # 替换 \n 为真正的换行
                formatted_notice = notice.replace('\\n', '\n')
                self.notice_text.setPlainText(formatted_notice)
            else:
                self.notice_text.setPlainText("暂无公告")
        except Exception as e:
            self.notice_text.setPlainText(f"公告加载失败: {str(e)}")
    
    def save_card_config(self, card):
        """保存卡密到主程序配置文件 ServerConfig.ini"""
        try:
            config = configparser.ConfigParser()
            config_file = 'ServerConfig.ini'
            if os.path.exists(config_file):
                config.read(config_file, encoding='utf-8')
            if 'AUTH' not in config:
                config.add_section('AUTH')
            config['AUTH']['last_card'] = card
            config['AUTH']['save_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            config['AUTH']['device_id'] = self.get_device_id()
            with open(config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            print(f"卡密已保存到 {config_file}: {card}")
        except Exception as e:
            print(f"保存卡密配置失败: {e}")

    def load_saved_card(self):
        """从主程序配置文件 ServerConfig.ini 加载保存的卡密"""
        try:
            config_file = 'ServerConfig.ini'
            print("加载卡密",config_file)
            if not os.path.exists(config_file):
                return
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            if 'AUTH' in config and 'last_card' in config['AUTH']:
                saved_card = config['AUTH']['last_card']
                if saved_card:
                    if saved_card == "TRIAL_MODE":
                        print("检测到上次使用试用模式")
                    else:
                        print("加载卡密",saved_card)
                        self.card_input.setText(saved_card)
                        print(f"已自动填充保存的卡密: {saved_card}")
                        if 'save_time' in config['AUTH']:
                            save_time = config['AUTH']['save_time']
                            print(f"卡密保存时间: {save_time}")
        except Exception as e:
            print(f"加载保存的卡密失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        try:
            self.auth.logout()
        except:
            pass
        event.accept()
    
    def mousePressEvent(self, event):
        """鼠标按下事件 - 记录拖拽起始位置"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖拽窗口"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def get_device_id(self):
        """获取设备唯一标识"""
        try:
            import hashlib
            return hashlib.md5(f"{platform.node()}{uuid.getnode()}".encode()).hexdigest()
        except Exception:
            return "unknown"


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("软件授权验证")
    app.setApplicationVersion("2.0.0")
    
    # 创建登录窗口
    login_window = LoginWindow()
    
    def on_login_success():
        print("✅ 登录验证成功！")
        # 这里可以启动主程序
        # 或者隐藏登录窗口，显示主界面
        
    login_window.login_success.connect(on_login_success)
    login_window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 