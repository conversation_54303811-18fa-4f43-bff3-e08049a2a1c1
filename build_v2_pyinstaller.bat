@echo off
REM === 自动打包 V2.py 为单文件可执行程序 ===

REM 1. 清理旧的dist和build目录
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build
if exist __pycache__ rmdir /s /q __pycache__

REM 2. 设置Python环境变量（如有需要可修改）
set PYTHONIOENCODING=utf-8

REM 3. 打包主程序
pyinstaller --noconfirm --clean --onefile --console --name V2  --icon=icon.ico V2.py

REM 4. 打包完成提示
if exist dist\V2.exe (
    echo.
    echo 打包成功！可执行文件在 dist\V2.exe
) else (
    echo.
    echo 打包失败，请检查错误信息。
)

pause 