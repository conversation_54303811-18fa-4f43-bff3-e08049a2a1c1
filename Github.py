import requests
from bs4 import BeautifulSoup
import random
import string
import json

USERNAME = "<EMAIL>"
PASSWORD = "d1551207823"
OWNER = "0xCodeY"  # 仓库所有者

def random_repo_name():
    return ''.join(random.choices(string.ascii_lowercase, k=8))

session = requests.Session()
session.headers.update({
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
})

# Step 1: 获取登录页面
login_page = session.get("https://github.com/login")
soup = BeautifulSoup(login_page.text, "html.parser")
authenticity_token = soup.find("input", {"name": "authenticity_token"})["value"]
timestamp = soup.find("input", {"name": "timestamp"})["value"]
timestamp_secret = soup.find("input", {"name": "timestamp_secret"})["value"]
required_field_input = soup.find("input", {"name": lambda x: x and x.startswith("required_field_")})
required_field = required_field_input["name"] if required_field_input else None

# Step 2: 登录
payload = {
    "commit": "Sign in",
    "authenticity_token": authenticity_token,
    "login": USERNAME,
    "password": PASSWORD,
    "webauthn-conditional": "undefined",
    "javascript-support": "true",
    "webauthn-support": "supported",
    "webauthn-iuvpaa-support": "unsupported",
    "return_to": "https://github.com/login",
    "allow_signup": "",
    "client_id": "",
    "integration": "",
    "timestamp": timestamp,
    "timestamp_secret": timestamp_secret
}
if required_field:
    payload[required_field] = ""

resp = session.post("https://github.com/session", data=payload, allow_redirects=False)

print(f"[+] 登录请求状态码: {resp.status_code}")

if resp.status_code == 302:
    print("[+] 登录成功")
    # Step 3: 获取创建仓库页面的 token
    new_repo_page = session.get("https://github.com/new")
    soup = BeautifulSoup(new_repo_page.text, "html.parser")
    create_token = soup.find("input", {"name": "authenticity_token"})["value"]
    print(f"[+] 创建仓库页面的 token: {create_token}")

    # Step 4: 随机仓库名
    repo_name = random_repo_name()
    print(f"[+] 创建仓库: {repo_name}")

    repo_data = {
        "owner": OWNER,
        "template_repository_id": "",
        "include_all_branches": "0",
        "repository": {
            "name": repo_name,
            "visibility": "public",
            "description": "",
            "auto_init": "0",
            "license_template": "",
            "gitignore_template": ""
        },
        "metrics": {
            "user_filtered_dropdown": False,
            "user_set_template": False,
            "user_changed_default_owner": False,
            "user_changed_owner_after_setting_template": False,
            "created_from_organization": False,
            "prepopulated_template": False,
            "owner_has_marketplace_apps": False,
            "user_interacted_with_marketplace_apps": False,
            "user_is_admin": False,
            "elapsed_ms": 232960.40000000596,
            "submit_clicked_count": 1,
            "submit_errors": [],
            "clicked_suggested_repo_name": True,
            "used_suggested_repo_name": True,
            "submitted_using_v2": True
        },
        "authenticity_token": create_token
    }

    headers = {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest"
    }

    print("[+] 发送创建仓库请求...")
    print(f"[+] 请求Cookies:{session.cookies.get_dict()}")
    create_resp = session.post("https://github.com/repositories", data=json.dumps(repo_data), headers=headers)

    if create_resp.status_code in (200, 201, 302):
        print("[+] 仓库创建成功")
        print("[+] 当前所有 Cookies:")
        for cookie in session.cookies:
            print(f"{cookie.name} = {cookie.value}")
    else:
        print(f"[-] 创建失败，状态码: {create_resp.status_code}, 返回: {create_resp.text}")

else:
    print("[-] 登录失败")
