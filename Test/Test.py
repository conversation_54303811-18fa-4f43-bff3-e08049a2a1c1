import websocket
import json
import subprocess
import time
import requests
import os

# ===== 配置 =====
chrome_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe"  # 你的 Chrome 路径
user_data_dir = r"C:\chrometemp_debug"
target_url = "https://599.com/live/"  # 入口页面
monitor_url = "https://fb-p.599.com/footballapi/core/matchlist/jbjbj/instant"  # 👈 要监控的接口

# ===== 启动 Chrome =====
if not os.path.exists(user_data_dir):
    os.makedirs(user_data_dir)

subprocess.Popen([
    chrome_path,
    "--remote-debugging-port=9222",
    "--no-first-run",
    "--no-default-browser-check",
    "--remote-allow-origins=*",
    f"--user-data-dir={user_data_dir}",
    target_url
])

time.sleep(2)

# ===== 获取调试 WebSocket URL =====
resp = requests.get("http://127.0.0.1:9222/json")
ws_url = resp.json()[0]["webSocketDebuggerUrl"]

# ===== 日志文件 =====
log_file = "requests_ws.log"
resp_file = "instant_response.json"

def log(msg: str):
    print(msg)
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(msg + "\n")

# 保存请求 id -> url
req_map = {}

def on_message(ws, message):
    try:
        msg = json.loads(message)
    except:
        return

    method = msg.get("method")

    # === HTTP 请求 ===
    if method == "Network.requestWillBeSent":
        req = msg["params"]["request"]
        req_id = msg["params"]["requestId"]
        url = req["url"]
        req_map[req_id] = url
        if monitor_url in url:
            log(f"[Request] {req['method']} {url}")

    # === HTTP 响应头 ===
    elif method == "Network.responseReceived":
        res = msg["params"]["response"]
        req_id = msg["params"]["requestId"]
        url = req_map.get(req_id, "")
        if monitor_url in url:
            log(f"[Response] {res['status']} {url}")
            # 请求响应 body
            ws.send(json.dumps({
                "id": 1000,
                "method": "Network.getResponseBody",
                "params": {"requestId": req_id}
            }))

    # === HTTP 响应 body ===
    elif msg.get("id") == 1000:
        body = msg.get("result", {}).get("body", "")
        if body:
            print(f"请求响应 Body: {body[:100]}")  # 只打印前 100 字符
            log(f"[Response Body] 已保存到 {resp_file}")
            with open(resp_file, "w", encoding="utf-8") as f:
                f.write(body)

    # === WebSocket 建立 ===
    elif method == "Network.webSocketCreated":
        log(f"[WebSocket Created] {msg['params']['url']}")

    # === WebSocket 收到消息 ===
    elif method == "Network.webSocketFrameReceived":
        payload = msg["params"]["response"]["payloadData"]
        log(f"[WS Recv] {payload}")

    # === WebSocket 发送消息 ===
    elif method == "Network.webSocketFrameSent":
        payload = msg["params"]["response"]["payloadData"]
        log(f"[WS Send] {payload}")

def on_open(ws):
    ws.send(json.dumps({"id": 1, "method": "Network.enable"}))
    log("✅ 已开启 Network 监听")

def on_error(ws, error):
    log(f"❌ Error: {error}")

def on_close(ws, close_status_code, close_msg):
    log("⚠️ WebSocket 断开，尝试重连...")

# ===== 运行 =====
if __name__ == "__main__":
    ws = websocket.WebSocketApp(
        ws_url,
        on_message=on_message,
        on_open=on_open,
        on_error=on_error,
        on_close=on_close
    )
    ws.run_forever()
