import requests
from bs4 import BeautifulSoup

# 初始化会话，保持 cookies
session = requests.Session()

# 模拟浏览器的请求头
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Referer": "https://github.com/join",
}

# 1. 访问 GitHub 注册页面，获取 Google OAuth 链接
github_signup_url = "https://github.com/join"
response = session.get(github_signup_url, headers=headers)
soup = BeautifulSoup(response.text, "html.parser")

# 找到 Google 登录链接（通常在 <a> 标签中，包含 Google OAuth URL）
google_oauth_link = None
for link in soup.find_all("a"):
    href = link.get("href")
    if href and "accounts.google.com" in href:
        google_oauth_link = href
        break

if not google_oauth_link:
    print("未找到 Google OAuth 链接")
    exit()

# 2. 访问 Google OAuth 页面
response = session.get(google_oauth_link, headers=headers)
soup = BeautifulSoup(response.text, "html.parser")

# 提取 Google 登录表单的隐藏字段（如 CSRF 令牌）
form = soup.find("form")
if not form:
    print("未找到 Google 登录表单")
    exit()

# 构造登录请求的数据
google_login_url = form["action"] if form else "https://accounts.google.com/signin/v2/identifier"
login_data = {
    "Email": "<EMAIL>",  # 替换为你的 Gmail 地址
    "Passwd": "your_password",                # 替换为你的 Gmail 密码
    # 其他隐藏字段需通过抓包获取（如 gxf、continue 等）
}

# 3. 提交 Google 登录请求
response = session.post(google_login_url, data=login_data, headers=headers)
if "myaccount.google.com" in response.url or "github.com" in response.url:
    print("Google 登录成功，跳转到授权页面")
else:
    print("Google 登录失败，检查请求参数或账号密码")
    exit()

# 4. 授权 GitHub 访问 Google 账号
# Google 会显示授权页面，需模拟点击“允许”按钮
# 抓包获取授权请求的 URL 和参数
auth_url = response.url  # 假设重定向到授权页面
auth_data = {
    "scope": "email profile",  # 根据抓包填写
    "state": "",               # 从抓包获取
    # 其他字段
}
response = session.post(auth_url, data=auth_data, headers=headers)

# 5. 返回 GitHub，完成注册
# GitHub 会要求设置用户名等信息
github_callback_url = response.url  # 假设返回 GitHub 回调 URL
username_data = {
    "user[login]": "your_desired_username",  # 替换为想要的 GitHub 用户名
    "user[email]": "<EMAIL>",
    # 其他字段（如 source、timestamp 等，需抓包获取）
}
response = session.post(github_callback_url, data=username_data, headers=headers)

if response.status_code == 200 and "github.com" in response.url:
    print("GitHub 账号注册成功")
else:
    print("注册失败，检查请求参数或 GitHub 响应")
