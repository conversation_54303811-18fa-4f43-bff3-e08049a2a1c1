# 完整版 PyQt5 + 外部 HttpServer.exe 启动/停止集成，去除内置 Flask 服务
import configparser
import sys
import re
import json
import time
import urllib3
import traceback
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLineEdit, QPushButton, QTableWidget, QTextEdit, QLabel,
    QTabWidget, QComboBox, QSizePolicy, QTableWidgetItem, QMessageBox, QMenu,
    QDialog, QHeaderView, QDesktopWidget, QFileDialog, QFormLayout, QDialogButtonBox,
    QCheckBox
)
from PyQt5.QtGui import QBrush, QColor
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
import requests
from bs4 import BeautifulSoup
from datetime import datetime
import logging
import subprocess
from functools import partial

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
# 日志配置
log_filename = f'crawler_{datetime.now().strftime("%Y%m%d")}.log'
logging.basicConfig(
    filename=log_filename,
    level=logging.INFO,
    format='【%(asctime)s】>> %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    encoding='utf-8'
)

# 添加调试信息
print("正在导入PyQt5模块...")
try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QLineEdit, QPushButton, QTableWidget, QTextEdit, QLabel,
        QTabWidget, QComboBox, QSizePolicy, QTableWidgetItem, QMessageBox, QMenu,
        QDialog, QHeaderView, QDesktopWidget, QFileDialog, QFormLayout, QDialogButtonBox,
        QCheckBox
    )
    from PyQt5.QtGui import QBrush, QColor
    from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
    print("PyQt5模块导入成功")
except ImportError as e:
    print(f"PyQt5模块导入失败: {e}")
    input("按回车键退出...")
    sys.exit(1)

print("正在导入其他模块...")
try:
    import requests
    from bs4 import BeautifulSoup
    from datetime import datetime
    import logging
    import subprocess
    from functools import partial
    print("其他模块导入成功")
except ImportError as e:
    print(f"其他模块导入失败: {e}")
    input("按回车键退出...")
    sys.exit(1)

print("正在配置urllib3...")
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 日志配置
print("正在配置日志...")
try:
    log_filename = f'crawler_{datetime.now().strftime("%Y%m%d")}.log'
    logging.basicConfig(
        filename=log_filename,
        level=logging.INFO,
        format='【%(asctime)s】>> %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        encoding='utf-8'
    )
    print(f"日志配置成功，日志文件: {log_filename}")
except Exception as e:
    print(f"日志配置失败: {e}")

# 订单爬取线程，使用 QThread 并发执行请求
class OrderFetcher(QThread):
    data_received = pyqtSignal(list)
    log_signal = pyqtSignal(str)
    retry_signal = pyqtSignal()
    update_account_signal = pyqtSignal(dict)
    request_relogin = pyqtSignal(dict)  # 新增：请求主线程重新登录
    relogin_result = pyqtSignal(object) # 新增：主线程回传登录结果

    def __init__(self, parent, domain, admin_info, login_type, delay=1.0, domains=None, account_info=None, monitor_member_id=None, global_ver=None):
        super().__init__(parent)
        self.domain = domain.rstrip('/')
        self.admin_info = admin_info
        self.login_type = login_type
        self.is_running = True
        self.session = requests.Session()
        self.sel_maxid = 0  # 原 CONST_ADMIN_Sel_MaxID
        self.retry_number = 0  # 原 RETRY_NUMBER
        self.delay = delay
        self.domains = domains or []
        self.account_info = account_info or {}
        self.monitor_member_id = monitor_member_id  # 新增：监控会员ID
        self.global_ver = global_ver
        self._relogin_result = None

        # 连接信号
        self.relogin_result.connect(self._on_relogin_result)

    def get_current_delay(self):
        """获取最新的延迟值"""
        try:
            if hasattr(self.parent(), 'delay_input'):
                return float(self.parent().delay_input.text())
        except (ValueError, AttributeError):
            pass
        return self.delay  # 如果获取失败，返回初始值

    def _on_relogin_result(self, result):
        self._relogin_result = result
        if hasattr(self, '_eventloop') and self._eventloop.isRunning():
            self._eventloop.quit()

    def run(self):
        from PyQt5.QtCore import QEventLoop
        while self.is_running:
            start_time = time.time()
            try:
                if not self.admin_info.get("uid") or not self.admin_info.get("cookies") or not self.admin_info.get('ver'):
                    self.log_signal.emit("请先完成代理登录")
                    time.sleep(1)
                    continue
                ver = self.global_ver
                url = f"{self.domain}/transform.php?ver={ver}"
                login_layer = "ag" if self.login_type == "登3" else "su"
                payload = (
                    f"login_layer={login_layer}&uid={self.admin_info['uid']}&langx=zh-cn&ver={self.admin_info['ver']}"
                    f"&p=get_wmc_list_bet&totalBets=wmc&gtype=FT&sel_maxid={self.sel_maxid}"
                )
                cookie_str = "; ".join([f"{k}={v}" for k, v in self.admin_info.get("cookies", {}).items()])
                headers = {
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Cookie": cookie_str,
                    "Host": re.sub(r'^https?://', '', self.domain),
                    "Origin": self.domain,
                    "Referer": f"{self.domain}/transform.php?ver={self.admin_info['ver']}",
                    "User-Agent": "Mozilla/5.0"
                }
                print("[调试] 发送请求的URL:", url)
                print("[调试] 发送请求的Payload:", payload)
                print("[调试] 发送请求的Headers:", headers)
                response = requests.post(url, data=payload, headers=headers, timeout=5,verify=False)
                text = response.text
                logging.info(f"获取订单响应: {text}")
                json_data = response.json()
                print("[调试] 获取订单响应JSON:", json_data)
                self.retry_number = 0
                # 检查无权限情况
                if json_data.get("status") == "error" and json_data.get("msg") == "goToHome":
                    self.log_signal.emit("账号无权限，已停止监控")
                    # 通知主窗口更新账号状态
                    update_info = {
                        "account": self.account_info.get("account"),
                        "admin_info": self.admin_info,
                        "status": "账号无权限"
                    }
                    self.update_account_signal.emit(update_info)
                    self.is_running = False
                    break
                if json_data.get("code", "") == "4X014" or json_data.get("code", "")=="4X042":
                    if not self.is_running:
                        break
                    self.log_signal.emit(f"账号 {self.account_info.get('account')} 登录状态错误，正在重新登录...")
                    print("检测到4X014，准备重登", self.account_info.get('account'))
                    new_admin_info = self.thread_admin_login(self.account_info)
                    print(f"重新登录结果: {new_admin_info}")
                    if not self.is_running:
                        break
                    if new_admin_info:
                        self.admin_info = new_admin_info
                        self.log_signal.emit("重新登录成功")
                        update_info = {
                            "account": self.account_info.get("account"),
                            "admin_info": new_admin_info,
                            "status": "已登录"
                        }
                        self.update_account_signal.emit(update_info)
                    else:
                        self.log_signal.emit("重新登录失败")
                        self.retry_signal.emit()
                    continue
                maxid = json_data.get("maxid", self.sel_maxid)
                if maxid != self.sel_maxid:
                    self.sel_maxid = maxid
                wagers = json_data.get("wagers", [])
                if wagers:
                    # 处理所有订单，但标记哪些是指定会员的订单
                    processed = self.process_wagers(wagers)
                    if processed:
                        self.data_received.emit(processed)
            except Exception as e:
                # self.retry_number += 1
                self.log_signal.emit(f"账号 {self.account_info.get('account')} 获取下单数据失败: (第{self.retry_number}次)")
                # if self.retry_number >= 3:
                #     self.log_signal.emit(f"连续失败3次，停止监控")
                #     self.retry_signal.emit()
            elapsed = time.time() - start_time
            # 动态获取最新的延迟值
            current_delay = self.get_current_delay()
            sleep_time = max(0, current_delay - elapsed)
            end = time.time() + sleep_time
            while time.time() < end:
                if not self.is_running:
                    break
                time.sleep(0.1)

    def stop(self):
        self.is_running = False
        try:
            self.session.close()
        except Exception:
            pass

    def process_wagers(self, wagers):
        wtype_map = {
            "大/小-上半场": "HOU", "让球-上半场": "HR", "大/小": "OU", "让球": "R",
            "(滚球)让球": "RE", "(滚球)大/小": "ROU", "(滚球)让球-上半场": "HRE",
            "(滚球)大/小-上半场": "HROU", "(滚球)角球数-大/小-上半场": "HROU",
            "(滚球)角球数-大/小": "ROU", "(滚球)角球数-让球-上半场": "HRE",
            "(滚球)角球数-让球": "RE",
        }
        new_data = []
        for wager in wagers:
            wagerstype = wager.get("WAGERSTYPE", "").replace(" ", "")
            wtype = wtype_map.get(wagerstype, "Unknown")
            if wtype == "Unknown":
                continue
            order_type = wager.get("ORDER_TYPE", "").replace(" ", "").strip()
            team_h = "".join(wager.get("TEAM_H", "").split())
            team_c = "".join(wager.get("TEAM_C", "").split())
            chose_team = ""
            if "大/小" in wagerstype:
                chose_team = "H" if order_type == "小" else "C"
            elif "让球" in wagerstype:
                chose_team = "H" if order_type == team_h else "C"
            match_name = re.split(r'<br>', wager.get('TNAME', ''))[0]
            score = self.get_score(wager.get('TNAME', ""))
            
            # 检查是否是指定会员的订单
            member_ids = [x.strip() for x in str(self.monitor_member_id).split(',') if x.strip()]
            is_monitored = (self.monitor_member_id and wager.get("DOWNLINE", "") in member_ids)
            
            data = {
                "Wid": wager.get("TID", ""),
                "MatchInfo": f"{match_name} - {team_h}-{team_c}",
                "MatchType": wagerstype,
                "PlayType": order_type,
                "Hdp": wager.get("ORDER_CON", ""),
                "OrderMoney": wager.get("GOLD", ""),
                "Score": score,
                "MemberID": wager.get("DOWNLINE", ""),
                "UserName": wager.get("NAME0", ""),
                "ts": time.time(),
                "is_monitored": is_monitored,  # 新增：标记是否是指定会员的订单
                "AddData": {
                    "p": "FT_order_view",
                    "uid": "",
                    "ver": "",
                    "langx": "zh-cn",
                    "odd_f_type": "H",
                    "gid": wager.get("_EVENT", ""),
                    "gtype": "FT",
                    "wtype": wtype,
                    "chose_team": chose_team
                },
                "CONST_ADMIN_Sel_MaxID": self.sel_maxid
            }
            new_data.append(data)
        return new_data

    def get_score(self, html_str):
        match = re.search(r'\(\d+\s*-\s*\d+\)', html_str)
        return match.group(0).strip('()').replace(' ', '') if match else "未开赛"

    def thread_admin_login(self, account_info):
        print("thread_admin_login被调用", account_info)
        login_type = account_info.get('LoginType')
        selected_domain = account_info.get('Domain', '').strip()
        username = account_info.get('Username', '').strip()
        password = account_info.get('Password', '').strip()
        if not selected_domain or not username or not password:
            self.log_signal.emit("登录失败：域名、账号或密码为空")
            return None
        if selected_domain.startswith("http://") or selected_domain.startswith("https://"):
            domain = selected_domain
        else:
            domain = f"https://{selected_domain}"
        if not self.global_ver:
            # 这里假设 parent 是 CrawlerWindow
            if hasattr(self.parent(), 'get_ver_from_domain'):
                self.global_ver = self.parent().get_ver_from_domain(domain)
            if not self.global_ver:
                self.log_signal.emit("无法获取ver，登录中止")
                return None
        ver = self.global_ver
        login_url = f"{domain}/transform.php"
        query_params = {"ver": ver}
        login_layer = "ag" if login_type == "登3" else "su"
        pwd_safe = account_info.get('SecurityCode', '').strip() if login_type == "登2" else "none"
        payload = (
            f"p=login_chk&ver={ver}&login_layer={login_layer}"
            f"&username={username}&pwd={password}&pwd_safe={pwd_safe}&uid=&blackbox="
        )
        headers = {
            "Accept": "*/*", "Content-Type": "application/x-www-form-urlencoded",
            "Host": re.sub(r'^https?://', '', domain), "Origin": domain, "Referer": domain + "/",
            "User-Agent": "Mozilla/5.0"
        }
        try:
            response = requests.post(login_url, data=payload, headers=headers, params=query_params, timeout=5, verify=False)
            logging.info(f"登录响应: {response.text}")
            logging.info(f"登录响应头: {response.headers}")
            json_data = response.json()
            print(f"登录响应JSON: {json_data}")
            if str(json_data.get("code")) == "201":
                self.log_signal.emit("本次登录需要验证码，请手动在网页完成登录")
                return None
            if json_data.get("status") == "success":
                self.log_signal.emit(f"账号 {username} 登录成功")
                set_cookie = response.headers.get('Set-Cookie', '')
                return {
                    "cookies": response.cookies.get_dict(),
                    "set_cookie": set_cookie,
                    "uid": json_data.get("uid"),
                    "ver": ver,
                    "layer_id": json_data.get('layer_id', '')
                }
            else:
                self.log_signal.emit(f"代理登录失败: {json_data.get('status')}")
                return None
        except Exception as e:
            self.log_signal.emit(f"登录请求失败或解析失败: {e}")
            return None

class AddAccountDialog(QDialog):
    def __init__(self, domains, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加/编辑代理账号")
        self.admin_info = None
        self.login_success = False
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()
        self.login_type_combo = QComboBox()
        self.login_type_combo.addItems(["登3", "登2"])
        self.login_type_combo.currentTextChanged.connect(self.toggle_security_code_input)
        self.domain_combo = QComboBox()
        self.domain_combo.addItems(domains)
        self.domain_combo.setEditable(True)
        self.account_input = QLineEdit()
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.security_code_label = QLabel("安全码：")
        self.security_code_input = QLineEdit()
        self.monitor_input = QLineEdit()
        form_layout.addRow("登录类型:", self.login_type_combo)
        form_layout.addRow("域名:", self.domain_combo)
        form_layout.addRow("账号:", self.account_input)
        form_layout.addRow("密码:", self.password_input)
        form_layout.addRow(self.security_code_label, self.security_code_input)
        form_layout.addRow("监控会员ID:", self.monitor_input)
        layout.addLayout(form_layout)
        action_layout = QHBoxLayout()
        self.login_button = QPushButton("登录测试")
        self.login_button.clicked.connect(self.test_login)
        self.select_member_button = QPushButton("选择会员")
        self.select_member_button.clicked.connect(self.select_member)
        self.select_member_button.setEnabled(False)
        action_layout.addWidget(self.login_button)
        action_layout.addWidget(self.select_member_button)
        layout.addLayout(action_layout)
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)
        self.toggle_security_code_input(self.login_type_combo.currentText())
    def toggle_security_code_input(self, login_type):
        is_登2 = (login_type == "登2")
        self.security_code_label.setVisible(is_登2)
        self.security_code_input.setVisible(is_登2)
    def test_login(self):
        account_details = self.get_details()
        if not all([account_details["domain"], account_details["account"], account_details["password"]]):
            QMessageBox.warning(self, "信息不完整", "请填写域名、账号和密码。")
            return
        # 直接调用主窗口的admin_login
        admin_info = self.parent().admin_login(account_details)
        self.on_login_result(admin_info)
    def on_login_result(self, admin_info):
        self.admin_info = admin_info
        if self.admin_info:
            self.login_success = True
            QMessageBox.information(self, "成功", "登录成功！现在您可以选择会员。")
            self.select_member_button.setEnabled(True)
        else:
            self.login_success = False
            QMessageBox.critical(self, "失败", "登录失败，请检查账号信息或查看主窗口日志。")
            self.select_member_button.setEnabled(False)
    def select_member(self):
        if not self.admin_info:
            QMessageBox.warning(self, "尚未登录", "请先点击登录测试并成功登录。")
            return
        account_details = self.get_details()
        domain = account_details['domain']
        login_type = account_details['login_type']
        member_id = self.parent().select_member_dialog(domain, self.admin_info, login_type)
        print("[调试] select_member 返回:", member_id, type(member_id))
        if member_id:
            self.monitor_input.setText(",".join(member_id))
        print("[调试] 当前输入框内容：", self.monitor_input.text())
    def get_details(self):
        details = {
            "login_type": self.login_type_combo.currentText(),
            "domain": self.domain_combo.currentText().strip(),
            "account": self.account_input.text().strip(),
            "password": self.password_input.text().strip(),
            "security_code": self.security_code_input.text().strip() if self.login_type_combo.currentText() == "登2" else "",
            "monitor": self.monitor_input.text().strip(),
            "status": "已登录" if self.login_success else "未登录"
        }
        details['Username'] = details['account']
        details['Password'] = details['password']
        details['SecurityCode'] = details['security_code']
        details['LoginType'] = details['login_type']
        details['Domain'] = details['domain']
        details['admin_info'] = self.admin_info if self.login_success else {}
        print("[调试] get_details 监控会员ID：", self.monitor_input.text())
        return details

class MemberSelectDialog(QDialog):
    def __init__(self, parent, domain, admin_info, login_layer):
        super().__init__(parent)
        self.setWindowTitle("选择会员")
        self.resize(500, 600)
        self.domain = domain.rstrip('/')
        self.admin_info = admin_info
        self.login_layer = login_layer
        layout = QVBoxLayout(self)
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["会员ID", "会员账号", "账号备注", "登录账户"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionMode(QTableWidget.MultiSelection)  # 支持多选
        layout.addWidget(self.table)
        self.table.cellDoubleClicked.connect(self.on_double_click)
        self.load_members()
        # 增加确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept)
        layout.addWidget(self.ok_button)
    def load_members(self):
        try:
            ver = self.parent().global_ver
            url = f"{self.domain}/transform.php?ver={ver}"
            querystring = {"ver": self.admin_info['ver']}
            LoginLayer = "ag" if self.login_layer == "登3" else "su"
            payload = "p=get_acc_mem_list&ver="+self.admin_info['ver']+"&uid="+self.admin_info['uid']+"&login_layer=ag&user_name=&enable=Y&ltype=ALL&sort_type=asc&sort_name=username&langx=zh-cn&up_id="+self.admin_info['layer_id']
            # 只取 key=value
            cookie_str = self.admin_info.get("set_cookie", "")
            if cookie_str:
                cookie_str = cookie_str.split(';')[0]
            else:
                cookie_str = "; ".join([f"{k}={v}" for k, v in self.admin_info.get("cookies", {}).items()])
            headers = {
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "zh-CN,zh;q=0.9",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded",
                "Cookie": cookie_str,
                "Host": re.sub(r'^https?://', '', self.domain),
                "Origin": self.domain,
                "Referer": f"{self.domain}/transform.php?ver={self.admin_info['ver']}",
                "User-Agent": "Mozilla/5.0"
            }
            response = requests.request("POST", url, data=payload, headers=headers, params=querystring)
            json_data = response.json()
            members = json_data.get('account', [])
            self.table.setRowCount(len(members))
            for i, mem in enumerate(members):
                mem_id = mem.get('id', '')
                username = mem.get('username', '')
                alias = mem.get('alias', '')
                passwd_safe = mem.get('passwd_safe', '')
                self.table.setItem(i, 0, QTableWidgetItem(str(mem_id)))
                self.table.setItem(i, 1, QTableWidgetItem(username))
                self.table.setItem(i, 2, QTableWidgetItem(alias))
                self.table.setItem(i, 3, QTableWidgetItem(passwd_safe))
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载会员列表失败: {e}")

    def on_double_click(self, row, col):
        pass  # 双击不再直接accept，改用按钮
    def get_selected_ids(self):
        selected_rows = self.table.selectionModel().selectedRows()
        print("[调试] get_selected_ids selected_rows:", selected_rows)
        ids = [self.table.item(row.row(), 0).text() for row in selected_rows]
        print("[调试] get_selected_ids 返回:", ids)
        return ids

class CrawlerWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("HG Server V1.2 (多账号版)")
        self.resize(1200, 870)
        self.account_pool = []  # 账号池
        self.domains = []
        self.order_data = []
        self.fetcher_threads = {}  # 多账号并发监控
        self.http_process = None
        self.is_service_running = False
        self.start_time = None
        self.global_ver = None  # 全局ver
        self.init_ui()
        self.load_config()
        # self.init_global_ver()  # 删除初始化时获取ver
    def init_ui(self):
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)
        # 控制面板
        control_layout = QHBoxLayout()
        self.port_input = QLineEdit("8080")
        self.port_input.setMaximumWidth(80)
        self.delay_input = QLineEdit("1")
        self.delay_input.setMaximumWidth(80)
        self.delay_input.textChanged.connect(self.on_delay_changed)
        self.start_stop_button = QPushButton("启动服务")
        self.start_stop_button.clicked.connect(self.toggle_service)
        self.test_button = QPushButton("测试")
        self.test_button.setMaximumWidth(80)
        self.test_button.clicked.connect(self.test_fetch)
        self.save_config_button = QPushButton("保存配置")
        self.save_config_button.setMaximumWidth(100)
        self.save_config_button.clicked.connect(self.save_config)
        self.clear_log_button = QPushButton("清除日志")
        self.clear_log_button.clicked.connect(self.clear_logs)
        self.skin_combo = QComboBox()
        self.skin_combo.addItems(["默认皮肤(绿色)", "蓝色皮肤"])
        self.skin_combo.setMaximumWidth(120)
        self.skin_combo.currentTextChanged.connect(self.change_skin)
        control_layout.addWidget(QLabel("端口:"))
        control_layout.addWidget(self.port_input)
        control_layout.addWidget(QLabel("爬取延迟(秒):"))
        control_layout.addWidget(self.delay_input)
        control_layout.addWidget(self.start_stop_button)
        control_layout.addWidget(self.test_button)
        control_layout.addWidget(self.save_config_button)
        control_layout.addWidget(self.clear_log_button)
        control_layout.addWidget(QLabel("皮肤:"))
        control_layout.addWidget(self.skin_combo)
        control_layout.addStretch()
        layout.addLayout(control_layout)
        # 账号池区域
        account_pool_layout = QHBoxLayout()
        account_pool_layout.addWidget(QLabel("代理账号池:"))
        self.agent_load_button = QPushButton("载入域名")
        self.agent_load_button.clicked.connect(self.load_agent_domains)
        self.add_account_button = QPushButton("添加账号")
        self.add_account_button.clicked.connect(self.add_account)
        self.import_accounts_button = QPushButton("导入账号")
        self.import_accounts_button.clicked.connect(self.import_accounts)
        account_pool_layout.addWidget(self.agent_load_button)
        account_pool_layout.addWidget(self.add_account_button)
        account_pool_layout.addWidget(self.import_accounts_button)
        account_pool_layout.addStretch()
        layout.addLayout(account_pool_layout)
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(6)
        self.account_table.setHorizontalHeaderLabels(["域名", "账号", "监控会员", "状态", "运行状态", "操作"])
        self.account_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.account_table)
        # Tab 控件: 下单数据, 历史数据, 账号历史, 历史详情
        self.tab_widget = QTabWidget()
        columns = ["订单号", "比赛信息", "下单类型", "下单玩法", "下单盘口", "下单金额", "下单比分", "下单会员"]
        self.order_table = QTableWidget()
        self.order_table.setColumnCount(len(columns))
        self.order_table.setHorizontalHeaderLabels(columns)
        header = self.order_table.horizontalHeader()
        self.order_table.setColumnWidth(0, 100)
        header.setSectionResizeMode(1, header.Stretch)
        self.order_table.setColumnWidth(2, 120)
        self.order_table.setColumnWidth(3, 100)
        self.order_table.setColumnWidth(4, 80)
        self.order_table.setColumnWidth(5, 80)
        self.order_table.setColumnWidth(6, 80)
        self.order_table.setColumnWidth(7, 180)
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(len(columns))
        self.history_table.setHorizontalHeaderLabels(columns)
        self.history_table.setColumnWidth(0, 100)
        header_history = self.history_table.horizontalHeader()
        header_history.setSectionResizeMode(1, header_history.Stretch)
        self.history_table.setColumnWidth(2, 120)
        self.history_table.setColumnWidth(3, 100)
        self.history_table.setColumnWidth(4, 80)
        self.history_table.setColumnWidth(5, 80)
        self.history_table.setColumnWidth(6, 80)
        self.history_table.setColumnWidth(7, 180)
        self.history_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.history_table.customContextMenuRequested.connect(self.show_history_context_menu)
        self.acc_history_widget = QWidget()
        acc_layout = QVBoxLayout(self.acc_history_widget)
        self.acc_summary_label = QLabel("总投注: -, 有效投注: -, 派彩结果: -")
        acc_layout.addWidget(self.acc_summary_label)
        self.acc_history_table = QTableWidget()
        acc_columns = ["日期", "星期", "投注额", "有效投注", "派彩结果", "操作"]
        self.acc_history_table.setColumnCount(len(acc_columns))
        self.acc_history_table.setHorizontalHeaderLabels(acc_columns)
        ah_header = self.acc_history_table.horizontalHeader()
        ah_header.setSectionResizeMode(0, QHeaderView.Stretch)
        acc_layout.addWidget(self.acc_history_table)
        self.detail_widget = QWidget()
        detail_layout = QVBoxLayout(self.detail_widget)
        detail_columns = ["订单号", "比赛信息", "下单金额", "下单水位", "下单时间", "下单盘口", "输赢"]
        self.detail_table = QTableWidget()
        self.detail_table.setColumnCount(len(detail_columns))
        self.detail_table.setHorizontalHeaderLabels(detail_columns)
        dt_header = self.detail_table.horizontalHeader()
        dt_header.setSectionResizeMode(0, QHeaderView.Interactive)
        self.detail_table.resizeColumnToContents(0)
        dt_header.resizeSection(0, 100)
        dt_header.setSectionResizeMode(1, QHeaderView.Interactive)
        self.detail_table.resizeColumnToContents(1)
        dt_header.resizeSection(1, 300)
        dt_header.setSectionResizeMode(2, QHeaderView.Interactive)
        self.detail_table.resizeColumnToContents(2)
        dt_header.resizeSection(2, 100)
        dt_header.setSectionResizeMode(3, QHeaderView.Interactive)
        self.detail_table.resizeColumnToContents(3)
        dt_header.setSectionResizeMode(4, QHeaderView.Interactive)
        self.detail_table.resizeColumnToContents(4)
        dt_header.resizeSection(4, 80)
        dt_header.setSectionResizeMode(5, QHeaderView.Interactive)
        self.detail_table.resizeColumnToContents(5)
        dt_header.resizeSection(5, 100)
        dt_header.setSectionResizeMode(6, QHeaderView.Stretch)
        dt_header.resizeSection(6, 100)
        detail_layout.addWidget(self.detail_table)
        self.tab_widget.addTab(self.order_table, "下单数据")
        self.tab_widget.addTab(self.history_table, "历史数据")
        self.tab_widget.addTab(self.acc_history_widget, "账号历史")
        self.tab_widget.addTab(self.detail_widget, "历史详情")
        # 域名列表Tab
        self.domain_list_widget = QWidget()
        domain_layout = QVBoxLayout(self.domain_list_widget)
        self.domain_list_table = QTableWidget()
        self.domain_list_table.setColumnCount(3)
        self.domain_list_table.setHorizontalHeaderLabels(["可用域名", "消息", "操作"])
        self.domain_list_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.domain_list_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.domain_list_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        domain_layout.addWidget(self.domain_list_table)
        self.tab_widget.addTab(self.domain_list_widget, "域名列表")
        # 推送设置Tab
        self.push_widget = QWidget()
        push_layout = QFormLayout(self.push_widget)
        self.push_enable_checkbox = QCheckBox("启用推送")
        self.push_amount_input = QLineEdit()
        self.push_uid_input = QTextEdit()
        self.push_uid_input.setPlaceholderText("每行一个UID")
        self.push_uid_input.setFixedHeight(60)
        self.push_save_btn = QPushButton("保存推送设置")
        self.push_save_btn.setMaximumWidth(120)
        self.push_save_btn.clicked.connect(self.save_push_config)
        self.push_test_btn = QPushButton("测试推送")
        self.push_test_btn.setMaximumWidth(120)
        self.push_test_btn.clicked.connect(self.test_push_message)
        push_layout.addRow("是否推送：", self.push_enable_checkbox)
        push_layout.addRow("推送金额：", self.push_amount_input)
        push_layout.addRow("推送UID：", self.push_uid_input)
        btn_layout = QHBoxLayout()
        btn_layout.addWidget(self.push_save_btn)
        btn_layout.addWidget(self.push_test_btn)
        push_layout.addRow(btn_layout)
        self.tab_widget.addTab(self.push_widget, "推送设置")
        layout.addWidget(self.tab_widget)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        bottom_layout = QHBoxLayout()
        self.runtime_label = QLabel("运行时间: 0天0小时0分0秒")
        self.author_label = QLabel("By: _0xAiCode")
        bottom_layout.addWidget(self.runtime_label)
        bottom_layout.addWidget(self.author_label, alignment=Qt.AlignRight)
        layout.addLayout(bottom_layout)
        self.skins = {
            "默认皮肤(绿色)": """
                QWidget { background-color: #f0f0f0; }
                QTableWidget { background-color: white; border: 1px solid #cccccc; gridline-color: #e0e0e0; }
                QTableWidget::item:selected { background-color: #90ee90; color: black; }
                QPushButton { background-color: #4CAF50; color: white; border: none; padding: 5px; }
                QPushButton:hover { background-color: #45a049; }
                QComboBox { background-color: white; border: 1px solid #cccccc; padding: 2px; }
            """,
            "蓝色皮肤": """
                QWidget { background-color: #e6f3ff; }
                QTableWidget { background-color: white; border: 1px solid #b3d9ff; gridline-color: #cce6ff; }
                QTableWidget::item:selected { background-color: #87ceeb; color: black; }
                QPushButton { background-color: #4682b4; color: white; border: none; padding: 5px; }
                QPushButton:hover { background-color: #4169e1; }
                QComboBox { background-color: white; border: 1px solid #b3d9ff; padding: 2px; }
            """
        }
        self.setStyleSheet(self.skins.get("默认皮肤(绿色)"))
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_runtime)
        self.timer.start(1000)
        self.setCentralWidget(main_widget)
    def load_agent_domains(self):
        self.domains = self.get_domains().get("admin", [])
        if self.domains:
            self.log(f"已载入 {len(self.domains)} 个域名列表")
            self.update_domain_list_table()
        else:
            self.log("未获取到可用域名")
            self.update_domain_list_table()
    def add_account(self):
        dialog = AddAccountDialog(self.domains, self)
        if dialog.exec_() == QDialog.Accepted:
            new_account = dialog.get_details()
            if not all([new_account["domain"], new_account["account"], new_account["password"]]):
                QMessageBox.warning(self, "提示", "域名、账号和密码不能为空！")
                return
            if not new_account["monitor"].strip():
                QMessageBox.warning(self, "提示", "请设置监控会员ID！")
                return
            self.account_pool.append(new_account)
            self.update_account_table()
            self.log(f"账号 {new_account['account']} 已添加到账号池，监控会员ID: {new_account['monitor']}")
    def delete_account(self):
        selected_rows = self.account_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "提示", "请先在表格中选择要删除的账号。")
            return
        reply = QMessageBox.question(self, '确认删除', '确定要删除选中的账号吗？',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            for index in sorted([r.row() for r in selected_rows], reverse=True):
                removed_account = self.account_pool.pop(index)
                self.log(f"账号 {removed_account['account']} 已从账号池中删除。")
            self.update_account_table()
    def import_accounts(self):
        options = QFileDialog.Options()
        filePath, _ = QFileDialog.getOpenFileName(self, "导入账号文件", "",
                                                  "文本文件 (*.txt);;所有文件 (*)", options=options)
        if filePath:
            try:
                with open(filePath, 'r', encoding='utf-8') as f:
                    count = 0
                    for line in f:
                        line = line.strip()
                        if not line:
                            continue
                        parts = []
                        if '----' in line:
                            parts = line.split('----', 1)
                        elif ',' in line:
                            parts = line.split(',', 1)
                        elif '，' in line:
                             parts = line.split('，', 1)
                        else:
                            self.log(f"无法解析行: {line}，请使用 '----' 或 ',' 分隔账号和密码。")
                            continue
                        if len(parts) == 2:
                            account, password = parts
                            new_account = {
                                "domain": "",
                                "account": account.strip(),
                                "password": password.strip(),
                                "monitor": "",
                                "status": "未登录",
                                "login_type": "登3",
                                "security_code": ""
                            }
                            self.account_pool.append(new_account)
                            count += 1
                        else:
                            self.log(f"格式错误，跳过此行: {line}")
                if count > 0:
                    self.update_account_table()
                    self.log(f"成功导入 {count} 个账号。")
                    QMessageBox.information(self, "成功", f"成功导入 {count} 个账号。\n注意：导入的账号需要手动设置监控会员ID才能启动监控。")
            except Exception as e:
                self.log(f"导入文件失败: {e}")
                QMessageBox.critical(self, "错误", f"导入文件失败: {e}")
    def update_account_table(self):
        self.account_table.setRowCount(0)
        for row, acc in enumerate(self.account_pool):
            self.account_table.insertRow(row)
            self.account_table.setItem(row, 0, QTableWidgetItem(acc.get("domain", "")))
            self.account_table.setItem(row, 1, QTableWidgetItem(acc.get("account", "")))
            # 监控会员ID列，未设置时显示红色
            monitor_id = acc.get("monitor", "")
            monitor_item = QTableWidgetItem(monitor_id)
            if not monitor_id.strip():
                monitor_item.setForeground(QBrush(QColor('red')))
                monitor_item.setText("未设置")
            self.account_table.setItem(row, 2, monitor_item)
            # 状态高亮
            status = acc.get("status", "未使用")
            status_item = QTableWidgetItem(status)
            if status == "已登录":
                status_item.setForeground(QBrush(QColor('green')))
            elif status == "未登录":
                status_item.setForeground(QBrush(QColor('red')))
            elif status == "账号无权限":
                status_item.setForeground(QBrush(QColor('orange')))
            self.account_table.setItem(row, 3, status_item)
            # 运行状态高亮
            run_status = "未运行"
            if status == "账号无权限":
                run_status = "账号无权限"
            elif hasattr(self, 'fetcher_threads') and acc.get("account") in getattr(self, 'fetcher_threads', {}):
                fetcher = self.fetcher_threads[acc.get("account")]
                if fetcher.isRunning():
                    run_status = "运行中"
            run_item = QTableWidgetItem(run_status)
            if run_status == "运行中":
                run_item.setForeground(QBrush(QColor('green')))
            elif run_status == "账号无权限":
                run_item.setForeground(QBrush(QColor('orange')))
            else:
                run_item.setForeground(QBrush(QColor('gray')))
            self.account_table.setItem(row, 4, run_item)
            # 操作按钮
            op_widget = QWidget()
            op_layout = QHBoxLayout(op_widget)
            op_layout.setContentsMargins(0, 0, 0, 0)
            btn_member = QPushButton("选择会员")
            btn_member.setMaximumWidth(80)
            btn_member.clicked.connect(partial(self.account_select_member, row))
            btn_history = QPushButton("加载历史")
            btn_history.setMaximumWidth(80)
            btn_history.clicked.connect(partial(self.account_load_history, row))
            btn_delete = QPushButton("删除")
            btn_delete.setMaximumWidth(60)
            btn_delete.clicked.connect(partial(self.account_delete, row))
            btn_login = QPushButton("登录")
            btn_login.setMaximumWidth(60)
            btn_login.clicked.connect(partial(self.account_login, row))
            op_layout.addWidget(btn_member)
            op_layout.addWidget(btn_history)
            op_layout.addWidget(btn_login)
            op_layout.addWidget(btn_delete)
            op_layout.addStretch()
            self.account_table.setCellWidget(row, 5, op_widget)
            btn_member.setEnabled(not self.is_service_running)
    def account_select_member(self, row):
        acc = self.account_pool[row]
        domain = acc.get("domain", "")
        login_type = acc.get("login_type", "登3")
        admin_info = acc.get("admin_info") or self.admin_login(acc)
        if not admin_info:
            self.log("请先登录账号")
            return
        member_ids = self.select_member_dialog(domain, admin_info, login_type)
        if member_ids:
            self.account_pool[row]["monitor"] = ",".join(member_ids)
            self.update_account_table()
            self.log(f"账号 {acc.get('account')} 监控会员已更新为 {self.account_pool[row]['monitor']}")
    def account_load_history(self, row):
        acc = self.account_pool[row]
        monitor_id = acc.get("monitor", "").strip()
        if not monitor_id:
            self.log("请先为账号设置监控会员ID")
            return
        # 需要登录信息
        admin_info = acc.get("admin_info") or self.admin_login(acc)
        if not admin_info:
            self.log(f"账号 {acc.get('account')} 登录失败，无法加载历史")
            return
        domain = acc.get("domain", "")
        login_type = acc.get("login_type", "登3")
        login_layer = "ag" if login_type == "登3" else "su"
        ver = self.global_ver
        url = f"{domain.rstrip('/')}/transform.php?ver={ver}"
        payload = {
            'login_layer': login_layer,
            'uid': admin_info.get('uid',''),
            'langx': 'zh-cn',
            'ver': ver,
            'p': 'get_history_data',
            'mid': monitor_id,
            'selGtype': 'ALL'
        }
        cookie_str = admin_info.get("set_cookie", "")
        if not cookie_str:
            cookie_str = "; ".join([f"{k}={v}" for k, v in admin_info.get("cookies", {}).items()])

        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "Cookie": cookie_str,
            "Host": re.sub(r'^https?://', '', domain.rstrip('/')),
            "Origin": domain.rstrip('/'),
            "Referer": f"{domain.rstrip('/')}/transform.php?ver={ver}",
            "User-Agent": "Mozilla/5.0"
        }
        try:
            session = requests.Session()
            response = session.post(url, data=payload, timeout=10, headers=headers)
            data = response.json()
            if data.get('status') != '200':
                self.log(f"获取账号历史失败: {data}")
                return
            history = data.get('history', [])
            total = data.get('total', {})
            gold = total.get('GOLD','-')
            vgold = total.get('VGOLD','-')
            winloss = total.get('WINLOSS','-')
            self.acc_summary_label.setText(f"总投注: {gold}, 有效投注: {vgold}, 派彩结果: {winloss}")
            self.acc_history_table.setRowCount(len(history))
            for i, item in enumerate(history):
                date_name = item.get('DATE_NAME','')
                week = item.get('WEEK','')
                gold_item = item.get('GOLD','-')
                vgold_item = item.get('VGOLD','-')
                win_item = item.get('WINLOSS','-')
                date_full = item.get('DATE','')
                self.acc_history_table.setItem(i, 0, QTableWidgetItem(date_name))
                self.acc_history_table.setItem(i, 1, QTableWidgetItem(week))
                self.acc_history_table.setItem(i, 2, QTableWidgetItem(gold_item))
                self.acc_history_table.setItem(i, 3, QTableWidgetItem(vgold_item))
                win_item_cell = QTableWidgetItem(win_item)
                try:
                    if win_item not in ['-', '']:
                        val = float(win_item.replace(',', ''))
                        if val < 0:
                            win_item_cell.setForeground(QBrush(QColor('red')))
                        else:
                            win_item_cell.setForeground(QBrush(QColor('green')))
                except:
                    pass
                self.acc_history_table.setItem(i, 4, win_item_cell)
                btn = QPushButton("详情")
                btn.clicked.connect(lambda _, m=monitor_id, d=date_full: self.load_history_detail(m, d))
                self.acc_history_table.setCellWidget(i, 5, btn)
            self.tab_widget.setCurrentWidget(self.acc_history_widget)
            self.log(f"已加载账号 {acc.get('account')} 的监控会员 {monitor_id} 历史")
        except Exception as e:
            self.log(f"加载账号历史出错: {e}")
    def admin_login(self, account_info):
        print("admin_login被调用", account_info)
        login_type = account_info.get('LoginType')
        selected_domain = account_info.get('Domain', '').strip()
        username = account_info.get('Username', '').strip()
        password = account_info.get('Password', '').strip()
        if not selected_domain or not username or not password:
            self.log("登录失败：域名、账号或密码为空")
            return None
        if selected_domain.startswith("http://") or selected_domain.startswith("https://"):
            domain = selected_domain
        else:
            domain = f"https://{selected_domain}"
        if not self.global_ver:
            self.global_ver = self.get_ver_from_domain(domain)
            if not self.global_ver:
                self.log("无法获取ver，登录中止")
                return None
        ver = self.global_ver
        login_url = f"{domain}/transform.php"
        query_params = {"ver": ver}
        login_layer = "ag" if login_type == "登3" else "su"
        pwd_safe = account_info.get('SecurityCode', '').strip() if login_type == "登2" else "none"
        payload = (
            f"p=login_chk&ver={ver}&login_layer={login_layer}"
            f"&username={username}&pwd={password}&pwd_safe={pwd_safe}&uid=&blackbox="
        )
        headers = {
            "Accept": "*/*", "Content-Type": "application/x-www-form-urlencoded",
            "Host": re.sub(r'^https?://', '', domain), "Origin": domain, "Referer": domain + "/",
            "User-Agent": "Mozilla/5.0"
        }
        try:
            response = requests.post(login_url, data=payload, headers=headers, params=query_params, timeout=5, verify=False)
            logging.info(f"登录响应: {response.text}")
            logging.info(f"登录响应头: {response.headers}")
            json_data = response.json()
            print(f"登录响应JSON: {json_data}")
            if str(json_data.get("code")) == "201":
                self.log("本次登录需要验证码，请手动在网页完成登录")
                return None
            if json_data.get("status") == "success":
                self.log(f"账号 {username} 登录成功")
                set_cookie = response.headers.get('Set-Cookie', '')
                return {
                    "cookies": response.cookies.get_dict(),
                    "set_cookie": set_cookie,
                    "uid": json_data.get("uid"),
                    "ver": ver,
                    "layer_id": json_data.get('layer_id', '')
                }
            else:
                self.log(f"代理登录失败: {json_data.get('status')}")
                return None
        except Exception as e:
            self.log(f"登录请求失败或解析失败: {e}")
            return None
    def select_member_dialog(self, domain, admin_info, login_type):
        if not (admin_info and admin_info.get('uid')):
            self.log("登录信息不完整，无法选择会员")
            QMessageBox.warning(self, "错误", "登录信息不完整，无法选择会员")
            return None
        if not domain.startswith('http'):
            domain = f"https://{domain}"
        login_layer = "ag" if login_type == "登3" else "su"
        dlg = MemberSelectDialog(self, domain, admin_info, login_layer)
        if dlg.exec_() == QDialog.Accepted:
            return dlg.get_selected_ids()
        return None
    def log(self, message):
        # 只做日志append和写文件，不做任何弹窗、sleep、循环
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        entry = f"【{timestamp}】>> {message}"
        self.log_text.append(entry)
        if self.log_text.document().blockCount() > 1000:
            self.log_text.clear()
            self.log_text.append("日志过多，自动清除旧记录")
        logging.info(message)
    def clear_logs(self):
        self.log_text.clear()
        self.log("日志已清除")
    def show_terms_dialog(self):
        terms = "本软件仅提供数据采集，请勿用于违法用途，违反规定自行承担相应法律责任。\n继续使用标识您接受，是否继续？"
        reply = QMessageBox.question(self, '使用条款', terms,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        return reply == QMessageBox.Yes
    def save_config(self):
        config = configparser.RawConfigParser()
        config['DEFAULT'] = {
            'Port': self.port_input.text(),
            'Delay': self.delay_input.text(),
            'Skin': self.skin_combo.currentText(),
        }
        config['AccountPool'] = {'pool': json.dumps(self.account_pool, ensure_ascii=False)}
        # 推送设置
        config['PUSH'] = {
            'enabled': str(self.push_enable_checkbox.isChecked()),
            'amount': self.push_amount_input.text(),
            'uid': self.push_uid_input.toPlainText(),
        }
        try:
            with open('ServerConfig.ini', 'w', encoding='utf-8') as cfg:
                config.write(cfg)
            self.log("配置已保存到 ServerConfig.ini")
        except Exception as e:
            self.log(f"保存配置失败: {e}")
    def load_config(self):
        config = configparser.RawConfigParser()
        if config.read('ServerConfig.ini', encoding='utf-8'):
            if 'DEFAULT' in config:
                default_cfg = config['DEFAULT']
                self.port_input.setText(default_cfg.get('Port', '8080'))
                self.delay_input.setText(default_cfg.get('Delay', '1'))
                skin = default_cfg.get('Skin', '默认皮肤(绿色)')
                self.skin_combo.setCurrentText(skin)
                self.change_skin(skin)
                self.log("已从 ServerConfig.ini 加载基础配置")
            if 'AccountPool' in config:
                pool_str = config['AccountPool'].get('pool', '[]')
                try:
                    self.account_pool = json.loads(pool_str)
                    self.update_account_table()
                    self.log(f"已加载 {len(self.account_pool)} 个账号从账号池。")
                except json.JSONDecodeError:
                    self.log("解析账号池配置失败。")
            # 加载推送设置
            self.load_push_config(config)
        else:
            self.log("未找到 ServerConfig.ini，使用默认配置")
            self.load_push_config()
    def load_push_config(self, config=None):
        if config is None:
            config = configparser.RawConfigParser()
            config.read('ServerConfig.ini', encoding='utf-8')
        self.push_enabled = config.getboolean('PUSH', 'enabled', fallback=False)
        self.push_amount = config.get('PUSH', 'amount', fallback='')
        self.push_uid = config.get('PUSH', 'uid', fallback='')
        self.push_enable_checkbox.setChecked(self.push_enabled)
        self.push_amount_input.setText(self.push_amount)
        self.push_uid_input.setPlainText(self.push_uid)
    def get_domains(self):
        url = "http://0088-vip.com/"
        result = {"admin": []}
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            html_content = response.text
        except Exception as e:
            self.log(f"无法访问域名列表: {e}")
            return result
        soup = BeautifulSoup(html_content, "html.parser")
        manage_section = soup.find("div", {"id": "list2"})
        if manage_section:
            manage_links = manage_section.find_all("a")
            # 只保留非IP的域名，先去掉协议头再判断
            for link in manage_links:
                title = link.get("title")
                if title:
                    domain = re.sub(r"^https?://", "", title)
                    if not re.match(r"^\d+\.\d+\.\d+\.\d+$", domain):
                        result["admin"].append(title)
        return result
    def update_runtime(self):
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            days = elapsed.days
            hours, remainder = divmod(elapsed.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            self.runtime_label.setText(f"运行时间: {days}天{hours}小时{minutes}分{seconds}秒")
        else:
            self.runtime_label.setText("运行时间: 0天0小时0分0秒")
    def change_skin(self, skin_name):
        style = self.skins.get(skin_name)
        if style:
            self.setStyleSheet(style)
            self.log(f"已切换到 {skin_name}")
    def test_fetch(self):
        self.log("测试功能待实现")
    def show_history_context_menu(self, pos):
        menu = QMenu(self)
        clear_action = menu.addAction("清空列表")
        action = menu.exec_(self.history_table.mapToGlobal(pos))
        if action == clear_action:
            self.clear_history_table()
    def clear_history_table(self):
        self.history_table.setRowCount(0)
        self.log("历史数据表格已清空")
    def toggle_service(self):
        if not self.is_service_running:
            if not self.account_pool:
                self.log("账号池为空，请先添加账号")
                return
            self.fetcher_threads = {}
            started = 0
            try:
                delay = float(self.delay_input.text())
            except Exception:
                delay = 1.0
            domains = self.domains  # 已经过滤过IP的域名列表
            for acc in self.account_pool:
                if acc.get("status") != "已登录":
                    self.log(f"账号 {acc.get('account')} 未登录，跳过")
                    continue
                domain = acc.get("domain")
                login_type = acc.get("login_type", "登3")
                admin_info = acc.get("admin_info")
                monitor_member_id = acc.get("monitor", "").strip()  # 获取监控会员ID
                if not (domain and admin_info):
                    self.log(f"账号 {acc.get('account')} 信息不完整，跳过")
                    continue
                if not monitor_member_id:
                    self.log(f"账号 {acc.get('account')} 未设置监控会员ID，跳过")
                    continue
                fetcher = OrderFetcher(self, domain, admin_info, login_type, delay, domains, acc, monitor_member_id, self.global_ver)
                fetcher.data_received.connect(self.update_order_table)
                fetcher.log_signal.connect(self.log)
                fetcher.retry_signal.connect(lambda acc=acc: self.handle_retry_login(acc))
                fetcher.update_account_signal.connect(self.update_account_from_signal)
                fetcher.start()
                self.fetcher_threads[acc.get("account")] = fetcher
                self.log(f"已启动账号 {acc.get('account')} 监控会员 {monitor_member_id}（将显示所有订单，但只保存指定会员订单）")
                started += 1
            if started == 0:
                self.log("没有可用账号启动监控")
                return
            # 启动 HttpServer.exe
            try:
                port = int(self.port_input.text())
            except ValueError:
                self.log("请输入有效端口号")
                return
            script_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
            exe_path = os.path.join(script_dir, 'HttpServer.exe')
            if not os.path.isfile(exe_path):
                self.log(f"未找到 HttpServer.exe: {exe_path}")
                self.http_process = None
            else:
                args = [exe_path, '-port', str(port)]
                self.http_process = subprocess.Popen(
                    args,
                    cwd=script_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    shell=False
                )
                self.log(f"已启动 HttpServer.exe，PID={self.http_process.pid}，端口: {port}")
            self.is_service_running = True
            self.start_time = datetime.now()
            self.start_stop_button.setText("停止服务")
            self.log(f"已启动 {started} 个账号的监控服务")
            self.update_account_table()
        else:
            self._stop_service()

    def _stop_service(self):
        for fetcher in self.fetcher_threads.values():
            fetcher.stop()
        self.fetcher_threads = {}
        self.is_service_running = False
        self.start_time = None
        self.start_stop_button.setText("启动服务")
        self.log("所有监控服务已停止")
        self.update_account_table()
        # 停止 HttpServer.exe
        if hasattr(self, 'http_process') and self.http_process:
            try:
                self.log("正在终止 HttpServer.exe 进程...")
                self.http_process.terminate()
                try:
                    self.http_process.wait(timeout=5)
                except Exception:
                    self.http_process.kill()
                self.log("HttpServer.exe 进程已停止")
            except Exception as e:
                self.log(f"终止 HttpServer.exe 进程时出错: {e}")
            finally:
                self.http_process = None

    def load_history_detail(self, mid, date_full):
        self.tab_widget.setCurrentWidget(self.detail_widget)
        self.detail_table.clearContents()
        self.detail_table.setRowCount(0)
        # 查找账号池中对应账号，获取登录信息
        acc = None
        for a in self.account_pool:
            if a.get('monitor', '') == mid:
                acc = a
                break
        if not acc:
            self.log(f"未找到监控会员ID为 {mid} 的账号")
            return
        admin_info = acc.get("admin_info") or self.admin_login(acc)
        if not admin_info:
            self.log(f"账号 {acc.get('account', '')} 登录失败，无法加载历史详情")
            return
        domain = acc.get("domain", "")
        login_type = acc.get("login_type", "登3")
        login_layer = "ag" if login_type == "登3" else "su"
        ver = self.global_ver
        url = f"{domain.rstrip('/')}/transform.php?ver={ver}"
        payload = (
            f"login_layer={login_layer}"
            f"&uid={admin_info['uid']}"
            f"&langx=zh-cn"
            f"&ver={ver}"
            f"&p=get_history_view"
            f"&mid={mid}"
            f"&selGtype=ALL"
            f"&today_gmt={date_full}"
            f"&tmp_flag=Y"
            f"&pay_type=1"   # 这里改为1
        )
        cookie_str = admin_info.get("set_cookie", "")
        if not cookie_str:
            cookie_str = "; ".join([f"{k}={v}" for k, v in admin_info.get("cookies", {}).items()])
        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "Cookie": cookie_str,
            "Host": re.sub(r'^https?://', '', domain.rstrip('/')),
            "Origin": domain.rstrip('/'),
            "Referer": f"{domain.rstrip('/')}/transform.php?ver={ver}",
            "User-Agent": "Mozilla/5.0"
        }
        try:
            response = requests.post(url, data=payload, timeout=10, headers=headers)
            data = response.json()
            if data.get('status') != '200':
                self.log(f"获取历史详情失败: {data}")
                self.detail_table.setRowCount(0)
                return
            wagers = data.get('wagers', [])
            self.detail_table.setRowCount(len(wagers))
            for i, item in enumerate(wagers):
                wid = item.get('WID', '')
                league = item.get('LEAGUE', '').strip()
                team_h = item.get('TEAM_H', '').strip()
                team_c = item.get('TEAM_C', '').strip()
                match_info = f"{league} - {team_h} - {team_c}"
                gold = item.get('GOLD', '')
                ioratio = item.get('IORATIO', '')
                addtime = item.get('ADDTIME', '')
                result = item.get('RESULT', '').strip().replace(f"<tt class='word_red'>", '').replace(f"</tt>", '')
                bet_ratio = item.get('BET_RATIO', '')
                market = f"{result}{bet_ratio}" if bet_ratio else result
                result_wl = item.get('RESULT_WL', '').strip()
                self.detail_table.setItem(i, 0, QTableWidgetItem(wid))
                self.detail_table.setItem(i, 1, QTableWidgetItem(match_info))
                self.detail_table.setItem(i, 2, QTableWidgetItem(gold))
                self.detail_table.setItem(i, 3, QTableWidgetItem(ioratio))
                self.detail_table.setItem(i, 4, QTableWidgetItem(addtime))
                self.detail_table.setItem(i, 5, QTableWidgetItem(market))
                cell = QTableWidgetItem(result_wl)
                if result_wl:
                    if "输" in result_wl:
                        cell.setForeground(QBrush(QColor('red')))
                    elif "赢" in result_wl:
                        cell.setForeground(QBrush(QColor('green')))
                self.detail_table.setItem(i, 6, cell)
            self.log(f"已加载历史详情: 会员ID={mid} 日期={date_full} 共{len(wagers)}条")
        except Exception as e:
            self.log(f"加载历史详情出错: {e}")

    def account_delete(self, row):
        acc = self.account_pool[row]
        reply = QMessageBox.question(self, '确认删除', f'确定要删除账号 {acc.get("account", "")} 吗？',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            removed_account = self.account_pool.pop(row)
            self.update_account_table()
            self.log(f"账号 {removed_account.get('account', '')} 已从账号池中删除。")

    def account_login(self, row):
        acc = self.account_pool[row]
        admin_info = self.admin_login(acc)
        self.on_login_result(admin_info, row)

    def on_login_result(self, admin_info, row):
        acc = self.account_pool[row]
        if admin_info:
            self.account_pool[row]["admin_info"] = admin_info
            self.account_pool[row]["status"] = "已登录"
            self.update_account_table()
            self.log(f"账号 {acc.get('account', '')} 登录成功。")
            # 新增：如果有监控线程，更新其admin_info
            account_name = acc.get("account")
            if account_name in self.fetcher_threads:
                fetcher = self.fetcher_threads[account_name]
                fetcher.admin_info = admin_info
        else:
            self.account_pool[row]["status"] = "未登录"
            self.update_account_table()
            self.log(f"账号 {acc.get('account', '')} 登录失败。")

    def closeEvent(self, event):
        reply = QMessageBox.question(self, '确认关闭', '确定要关闭程序吗？',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.save_config()  # 关闭时保存账号池
            if self.is_service_running:
                QTimer.singleShot(0, self._stop_service)
            event.accept()
        else:
            event.ignore()

    def update_order_table(self, new_data):
        # 合并新数据到 self.order_data，避免重复
        existing_ids = {x['Wid'] for x in self.order_data}
        filtered = [d for d in new_data if d.get('Wid') not in existing_ids]
        if filtered:
            self.order_data.extend(filtered)
            if len(self.order_data) > 2000:
                self.order_data = self.order_data[-2000:]
            current = self.order_table.rowCount()
            self.order_table.setRowCount(current + len(filtered))
            monitored_orders = []
            for i, data in enumerate(filtered, start=current):
                user_str = f"{data.get('MemberID','')}-{data.get('UserName','')}"
                self.order_table.setItem(i, 0, QTableWidgetItem(str(data.get('Wid',''))))
                self.order_table.setItem(i, 1, QTableWidgetItem(data.get('MatchInfo','')))
                self.order_table.setItem(i, 2, QTableWidgetItem(data.get('MatchType','')))
                self.order_table.setItem(i, 3, QTableWidgetItem(data.get('PlayType','')))
                self.order_table.setItem(i, 4, QTableWidgetItem(data.get('Hdp','')))
                self.order_table.setItem(i, 5, QTableWidgetItem(str(data.get('OrderMoney',''))))
                self.order_table.setItem(i, 6, QTableWidgetItem(data.get('Score','')))
                self.order_table.setItem(i, 7, QTableWidgetItem(user_str))
                # 如果是指定会员的订单，添加到监控列表并记录日志
                if data.get('is_monitored', False):
                    monitored_orders.append(data)
                    self.log(f"监控到会员 {data.get('MemberID')} 的新订单: {data.get('MatchInfo')} - {data.get('PlayType')} - {data.get('Hdp')} - {data.get('OrderMoney')} - {data.get('UserName')}")
                else:
                    self.log(f"其他会员 {data.get('MemberID')} 的订单: {data.get('MatchInfo')} - {data.get('PlayType')} - {data.get('Hdp')} - {data.get('OrderMoney')} - {data.get('UserName')}")
            # 只将指定会员的订单写入 Order.json，并推送
            if monitored_orders:
                try:
                    order_file = 'Order.json'
                    try:
                        with open(order_file, 'r', encoding='utf-8') as f:
                            all_orders = json.load(f)
                    except Exception:
                        all_orders = []
                    all_orders.extend(monitored_orders)
                    with open(order_file, 'w', encoding='utf-8') as f:
                        json.dump(all_orders, f, ensure_ascii=False, indent=4)
                    self.log(f"已将 {len(monitored_orders)} 个指定会员订单写入 Order.json")
                except Exception as e:
                    self.log(f"写入 Order.json 失败: {e}")
                # 推送逻辑
                try:
                    push_enabled = self.push_enable_checkbox.isChecked()
                    push_amount = float(self.push_amount_input.text() or 0)
                    push_uid = self.push_uid_input.toPlainText()
                except Exception:
                    push_enabled = False
                    push_amount = 0
                    push_uid = ''
                if push_enabled and push_uid:
                    for order in monitored_orders:
                        try:
                            money = float(order.get('OrderMoney', 0))
                        except Exception:
                            money = 0
                        if money > push_amount:
                            # 构造适合手机端的推送内容
                            content = (
                                f"<b style='color:#2196F3;font-size:18px;'>大额订单提醒</b><br>"
                                f"<b>订单号：</b>{order.get('Wid','')}<br>"
                                f"<b>比赛：</b>{order.get('MatchInfo','')}<br>"
                                f"<b>类型：</b>{order.get('MatchType','')}<br>"
                                f"<b>玩法：</b>{order.get('PlayType','')}<br>"
                                f"<b>盘口：</b>{order.get('Hdp','')}<br>"
                                f"<b>金额：</b><span style='color:red;font-weight:bold;'>{order.get('OrderMoney','')}</span><br>"
                                f"<b>比分：</b>{order.get('Score','')}<br>"
                                f"<b>会员：</b>{order.get('MemberID','')}-{order.get('UserName','')}<br>"
                                f"<b>代理账号：</b>{order.get('account','') or (order.get('代理账号','') if '代理账号' in order else '')}<br>"
                                f"<span style='color:#888;font-size:12px;'>监控时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>"
                            )
                            summary = f"{order.get('MatchInfo','')} {order.get('OrderMoney','')}"
                            self.send_push_message(content, summary)

    def update_domain_list_table(self):
        domains = self.domains
        self.domain_list_table.setRowCount(len(domains))
        for i, d in enumerate(domains):
            self.domain_list_table.setItem(i, 0, QTableWidgetItem(d))
            msg_item = QTableWidgetItem("")
            self.domain_list_table.setItem(i, 1, msg_item)
            op_widget = QWidget()
            op_layout = QHBoxLayout(op_widget)
            op_layout.setContentsMargins(0, 0, 0, 0)
            btn_ping = QPushButton("检测延迟")
            btn_ping.setMaximumWidth(80)
            btn_ping.clicked.connect(lambda _, row=i: self.domain_ping(row))
            btn_ver = QPushButton("获取Ver")
            btn_ver.setMaximumWidth(80)
            btn_ver.clicked.connect(lambda _, row=i: self.domain_get_ver(row))
            op_layout.addWidget(btn_ping)
            op_layout.addWidget(btn_ver)
            op_layout.addStretch()
            self.domain_list_table.setCellWidget(i, 2, op_widget)

    def domain_ping(self, row):
        domain = self.domain_list_table.item(row, 0).text()
        import time
        import requests
        url = domain if domain.startswith('http') else f'https://{domain}'
        t1 = time.time()
        try:
            resp = requests.get(url, timeout=5)
            t2 = time.time()
            msg = f"延迟: {int((t2-t1)*1000)}ms 状态: {resp.status_code}"
        except Exception as e:
            msg = f"请求失败: {e}"
        self.domain_list_table.setItem(row, 1, QTableWidgetItem(msg))

    def domain_get_ver(self, row):
        domain = self.domain_list_table.item(row, 0).text()
        import re
        import requests
        url = domain if domain.startswith('http') else f'https://{domain}'
        try:
            resp = requests.get(url, timeout=5)
            html = resp.text
            ver_match = re.search(r"top\.ver\s*=\s*'([^']*)'", html)
            if ver_match:
                msg = f"ver: {ver_match.group(1)}"
            else:
                msg = "未找到ver"
        except Exception as e:
            msg = f"请求失败: {e}"
        self.domain_list_table.setItem(row, 1, QTableWidgetItem(msg))

    def handle_retry_login(self, acc):
        """处理重新登录失败的情况"""
        self.log(f"账号 {acc.get('account')} 重新登录失败")
        
        # 尝试重新登录
        admin_info = self.admin_login(acc)
        if admin_info:
            # 更新账号池中的登录信息
            for i, account in enumerate(self.account_pool):
                if account.get("account") == acc.get("account"):
                    self.account_pool[i]["admin_info"] = admin_info
                    self.account_pool[i]["status"] = "已登录"
                    break
            
            # 更新对应的fetcher线程
            if acc.get("account") in self.fetcher_threads:
                fetcher = self.fetcher_threads[acc.get("account")]
                fetcher.admin_info = admin_info
            
            self.update_account_table()
            self.log(f"账号 {acc.get('account')} 重新登录成功")
        else:
            self.log(f"账号 {acc.get('account')} 重新登录失败")
            # 更新账号状态为未登录
            for i, account in enumerate(self.account_pool):
                if account.get("account") == acc.get("account"):
                    self.account_pool[i]["status"] = "未登录"
                    break
            self.update_account_table()

    def update_account_from_signal(self, update_info):
        """从信号接收到的更新信息更新账号池"""
        account_name = update_info.get("account")
        for i, acc in enumerate(self.account_pool):
            if acc.get("account") == account_name:
                # 更新登录信息和状态
                self.account_pool[i]["admin_info"] = update_info.get("admin_info")
                self.account_pool[i]["status"] = update_info.get("status")
                break
        self.update_account_table()

    def get_ver_from_domain(self, domain):
        import re, requests
        url = domain if domain.startswith('http') else f'https://{domain}'
        try:
            resp = requests.get(url, timeout=10, verify=False)
            html = resp.text
            ver_match = re.search(r"top\.ver\s*=\s*'([^']*)'", html)
            if ver_match:
                return ver_match.group(1)
        except Exception as e:
            self.log(f"获取ver失败: {e}")
        return None

    def on_fetcher_request_relogin(self, account_info):
        # 主线程执行admin_login
        admin_info = self.admin_login(account_info)
        # 找到对应fetcher
        acc_name = account_info.get('account')
        fetcher = self.fetcher_threads.get(acc_name)
        if fetcher:
            fetcher.relogin_result.emit(admin_info)

    def on_delay_changed(self):
        """处理延迟值变化的逻辑"""
        try:
            new_delay = float(self.delay_input.text())
            if self.is_service_running:
                self.log(f"延迟值已更新为 {new_delay} 秒，正在运行的监控线程将使用新延迟值")
        except ValueError:
            # 如果输入的不是有效数字，不记录日志
            pass

    def send_push_message(self, content, summary=None):
        """推送消息到WxPusher"""
        url = "https://wxpusher.zjiecode.com/api/send/message"
        headers = {"Content-Type": "application/json"}
        app_token = "AT_yTDEgDCdt4olq1fY0CpZJipF5zhIPYCH"  # TODO: 替换为你的AppToken
        # 多行UID，去除空行
        uids = [uid.strip() for uid in self.push_uid_input.toPlainText().splitlines() if uid.strip()]
        data = {
            "appToken": app_token,
            "content": content,
            "summary": summary or content[:20],
            "contentType": 2,
            "uids": uids,
            "url": "https://wxpusher.zjiecode.com",
            "verifyPayType": 0
        }
        try:
            resp = requests.post(url, headers=headers, data=json.dumps(data), timeout=10)
            if resp.status_code == 200:
                self.log(f"推送成功: {content}")
            else:
                self.log(f"推送失败: {resp.text}")
        except Exception as e:
            self.log(f"推送异常: {e}")

    def save_push_config(self):
        self.push_enabled = self.push_enable_checkbox.isChecked()
        self.push_amount = self.push_amount_input.text()
        self.push_uid = self.push_uid_input.toPlainText()
        self.save_config()
        self.log("推送设置已保存")

    def test_push_message(self):
        # 构造适合手机端的测试推送内容
        content = (
            "<b style='color:#4CAF50;font-size:18px;'>推送测试</b><br>"
            "<b>订单号：</b>TEST123456<br>"
            "<b>比赛：</b>测试联赛 - 主队-客队<br>"
            "<b>类型：</b>单关<br>"
            "<b>玩法：</b>让球<br>"
            "<b>盘口：</b>1.5<br>"
            "<b>金额：</b><span style='color:red;font-weight:bold;'>9999</span><br>"
            "<b>比分：</b>0-0<br>"
            "<b>会员：</b>123456-张三<br>"
            "<b>代理账号：</b>测试代理<br>"
            "<span style='color:#888;font-size:12px;'>本消息为推送功能测试</span>"
        )
        summary = "推送测试"
        self.send_push_message(content, summary)

if __name__ == '__main__':
    print("程序启动中...")
    
    def excepthook(type, value, tb):
        error_msg = f"程序发生未捕获错误:\n{value}\n\n详细错误信息:\n{traceback.format_exc()}"
        print(error_msg)
        logging.error("Uncaught exception", exc_info=(type, value, tb))
        try:
            QMessageBox.critical(None, "错误", error_msg)
            # 自动退出，防止卡死
            from PyQt5.QtCore import QTimer
            app = QApplication.instance()
            if app:
                QTimer.singleShot(0, app.quit)
        except:
            print("无法显示错误对话框，错误信息已打印到控制台")
        # 不再input，不再阻塞，直接return让Qt事件循环退出
        return
    
    sys.excepthook = excepthook
    
    try:
        print("正在创建QApplication...")
        app = QApplication(sys.argv)
        print("QApplication创建成功")
        
        print("正在创建主窗口...")
        window = CrawlerWindow()
        print("主窗口创建成功")
        
        print("正在显示使用条款对话框...")
        if not window.show_terms_dialog():
            print("用户拒绝使用条款，程序退出")
            sys.exit(0)
        
        print("正在显示主窗口...")
        window.show()
        print("主窗口显示成功，进入事件循环...")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        error_msg = f"程序启动失败:\n{str(e)}\n\n详细错误信息:\n{traceback.format_exc()}"
        print(error_msg)
        
        # 尝试显示错误对话框
        try:
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            print("无法显示错误对话框")
        
        # 等待用户按键
        input("按回车键退出程序...")
        sys.exit(1)
