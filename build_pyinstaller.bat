@echo off
chcp 65001 >nul

echo 正在打包 HG监控系统...
echo.

REM 清理之前的打包文件
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "*.spec" del /q *.spec

REM PyInstaller打包命令 - 增强兼容性版本
pyinstaller ^
    --onefile ^
    --windowed ^
    --name=HG监控系统 ^
    --icon=icon.ico ^
    --add-data="Login.py;." ^
    --add-data="icon_helper.py;." ^
    --add-data="about_dialog.py;." ^
    --add-data="TG.png;." ^
    --add-data="icon.ico;." ^
    --hidden-import=PyQt6 ^
    --hidden-import=PyQt6.QtCore ^
    --hidden-import=PyQt6.QtGui ^
    --hidden-import=PyQt6.QtWidgets ^
    --hidden-import=PyQt6.sip ^
    --hidden-import=sip ^
    --hidden-import=requests ^
    --hidden-import=urllib3 ^
    --hidden-import=certifi ^
    --hidden-import=hashlib ^
    --hidden-import=uuid ^
    --hidden-import=platform ^
    --hidden-import=json ^
    --hidden-import=time ^
    --hidden-import=logging ^
    --hidden-import=traceback ^
    --hidden-import=configparser ^
    --hidden-import=subprocess ^
    --hidden-import=smtplib ^
    --hidden-import=email.mime.text ^
    --hidden-import=email.mime.multipart ^
    --hidden-import=threading ^
    --hidden-import=queue ^
    --hidden-import=datetime ^
    --hidden-import=re ^
    --hidden-import=os ^
    --hidden-import=sys ^
    --collect-all=PyQt6 ^
    --collect-all=requests ^
    --collect-all=urllib3 ^
    --collect-all=certifi ^
    --collect-submodules=PyQt6 ^
    --copy-metadata=PyQt6 ^
    --copy-metadata=requests ^
    --copy-metadata=urllib3 ^
    --copy-metadata=certifi ^
    --noconfirm ^
    QT6.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 打包成功！
    echo 📁 输出目录: dist\
    echo 🚀 可执行文件: dist\HG监控系统.exe
) else (
    echo.
    echo ❌ 打包失败！错误代码: %ERRORLEVEL%
)

pause 